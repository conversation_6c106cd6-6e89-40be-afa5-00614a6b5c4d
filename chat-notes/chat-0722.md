1、我发现现在 web 端工程中，关于组合、持仓这个模块的 UI 代码中，分散定义了好多 interface 类型，这不符合开发规范，这部分优化一下。
2、交易记录对话框需要再优化一下，不够好看，然后需要添加 action 列，支持修改记录和删除记录。
3、请遵守提供的开发规则

第二次对话

# 1.启动前后端服务

# 2.现在买入卖出是两个不同的对话框组件，不是很通用，我系统重新设计好让这个对话框支持买入，卖出，合股，拆股，除权除息等五种操作，具体需求如下描述

    ## 1.买入，卖出，合股，拆股，除权除息这五种属于交易类型，在交易对话框中有一个‘交易类型’的Select组件来支持不同的交易类型选择
    ## 2.选择对应的交易类型后对话框会渲染对应类型需要的字段供填写
    ## 3.具体的交易类型对应的字段内容如下
        1) 买入 需要填写的字段有：品种代码，所属组合，委托日期，买入价，买入量，佣金，税费，备注
        2) 卖出 需要填写的字段有：品种代码，所属组合，委托日期，卖出价，卖出量，佣金，税费，备注
        3) 合股 需要填写的字段有：品种代码，所属组合，委托日期，多股合一（多少股合为1股），备注
        4) 拆股 需要填写的字段有：品种代码，所属组合，委托日期，每股拆为（每1股拆为多少股），备注
        5) 除权除息 需要填写的字段有：品种代码，所属组合，委托日期，每10股转增，每10股送股，每10股红利，税费，备注

# 3. 注意代码复杂度和可读性，如果代码太长，需要拆分多个小组件
