1、我发现现在 web 端工程中，关于组合、持仓这个模块的 UI 代码中，分散定义了好多 interface 类型，这不符合开发规范，这部分优化一下。
2、交易记录对话框需要再优化一下，不够好看，然后需要添加 action 列，支持修改记录和删除记录。
3、请遵守提供的开发规则

第二次对话

# 1.启动前后端服务

# 2.现在买入卖出是两个不同的对话框组件，不是很通用，我系统重新设计好让这个对话框支持买入，卖出，合股，拆股，除权除息等五种操作，具体需求如下描述

    ## 1.买入，卖出，合股，拆股，除权除息这五种属于交易类型，在交易对话框中有一个‘交易类型’的Select组件来支持不同的交易类型选择
    ## 2.选择对应的交易类型后对话框会渲染对应类型需要的字段供填写
    ## 3.具体的交易类型对应的字段内容如下
        1) 买入 需要填写的字段有：品种代码，所属组合，委托日期，买入价，买入量，佣金，税费，备注
        2) 卖出 需要填写的字段有：品种代码，所属组合，委托日期，卖出价，卖出量，佣金，税费，备注
        3) 合股 需要填写的字段有：品种代码，所属组合，委托日期，多股合一（多少股合为1股），备注
        4) 拆股 需要填写的字段有：品种代码，所属组合，委托日期，每股拆为（每1股拆为多少股），备注
        5) 除权除息 需要填写的字段有：品种代码，所属组合，委托日期，每10股转增，每10股送股，每10股红利，税费，备注

# 3. 注意代码复杂度和可读性，如果代码太长，需要拆分多个小组件

第三次对话

几点需要优化
1、universal-transaction-dialog 和 edit-transaction-dialog 应该整合成一个。
2、dialog 的 title 统一都叫“交易”，不需要 description
3、对话框中 label 和对应的输入控件的布局应该用左右布局，一行一个属性，备注可以跨多行
4、布局的顺序严格按照上面提供的交易类型对应的字段顺序来
5、dialog 的背景色不好看，参考一下 dashboard/crm 下的表格背景颜色
6、dialog 确认按钮统一文字“确认”就好
7、持仓表格 action 列中，去掉买入卖出菜单，新增“添加”菜单，“查看交易”菜单改名为“记录”
8、点击某交易按钮，弹出的对话框中交易类型要选择对应的类型，比如点击买入，对应的交易类型要选择买入，如果是点击“添加交易”，则该交易类型放空，让用户选择
9、持仓/交易记录/转账记录 tab 组件右侧的按钮只保留 买入，卖出 和 银证转账

以下是优化提示词后的表述
请对通用交易对话框系统进行以下 9 项具体优化：

## 1. 组件整合

-   将 `universal-transaction-dialog.tsx` 和 `edit-transaction-dialog.tsx` 合并为一个统一的交易对话框组件
-   新组件应支持创建新交易和编辑现有交易两种模式
-   通过 props 参数区分是新建模式还是编辑模式

## 2. 对话框标题简化

-   所有交易对话框的 DialogTitle 统一设置为"交易"
-   移除 DialogDescription 组件，不显示描述文字

## 3. 表单布局重构

-   将当前的网格布局改为左右布局：label 在左侧，输入控件在右侧
-   每行显示一个属性字段
-   备注字段可以跨多行显示（使用 textarea）
-   使用合适的间距和对齐方式

## 4. 字段顺序标准化

严格按照以下顺序排列表单字段：

-   **买入**: 品种代码 → 所属组合 → 委托日期 → 买入价 → 买入量 → 佣金 → 税费 → 备注
-   **卖出**: 品种代码 → 所属组合 → 委托日期 → 卖出价 → 卖出量 → 佣金 → 税费 → 备注
-   **合股**: 品种代码 → 所属组合 → 委托日期 → 多股合一 → 备注
-   **拆股**: 品种代码 → 所属组合 → 委托日期 → 每股拆为 → 备注
-   **除权除息**: 品种代码 → 所属组合 → 委托日期 → 每 10 股转增 → 每 10 股送股 → 每 10 股红利 → 税费 → 备注

## 5. 对话框样式优化

-   参考 `dashboard/crm` 目录下表格的背景颜色样式
-   更新 DialogContent 的背景色和整体视觉效果
-   确保与系统整体设计风格一致

## 6. 按钮文字统一

-   所有交易对话框的确认按钮文字统一为"确认"
-   移除动态的按钮文字（如"确认买入"、"确认卖出"等）

## 7. 持仓表格操作菜单调整

在 `holdings-table.tsx` 的 action 列中：

-   **移除**: "买入"和"卖出"菜单项
-   **新增**: "添加"菜单项（用于添加新交易）
-   **重命名**: 将"查看交易"改为"记录"

## 8. 交易类型预选逻辑

-   点击具体交易按钮（买入/卖出等）时，对话框中的交易类型应预选对应类型且禁用修改
-   点击"添加交易"按钮时，交易类型选择器为空，允许用户自由选择
-   确保交易类型与触发按钮的一致性

## 9. 工具栏按钮精简

在持仓/交易记录/转账记录 tab 组件的右侧工具栏中：

-   **保留**: 买入、卖出、银证转账 三个按钮
-   **移除**: 合股、拆股、除权除息按钮
-   这些高级交易功能通过持仓表格的"添加"菜单访问

## 10. 委托日期优化

-   只需要选择到日期，不需要选择到时分秒
-   datepicker 使用 shadcn-ui 的组件，不要使用原声组件

请确保所有修改都保持代码的类型安全性和现有功能的完整性。

第四次对话
transaction-history-dialog 需要再优化一下
1、样式参考 transaction-dialog
2、目前的样式有 bug，表格宽度超出对话框宽度了
3、表格列需要重新设置按我提供的这个来：“交易日期 类型 成交价/数量 税费/佣金 成交金额 说明 备注 操作” 其中“成交价/数量” 和 “税费/佣金”这两列需要合并展示，用上下布局来显示
4、数据多时表格需要支持分页
5、备注列文字比较多，需要宽度比较宽，然后支持省略号显示，鼠标移上去显示全部内容
6、这个记录对话框不能太小，大小要合适
7、transfers-table.tsx 有一个报错：Argument of type 'string' is not assignable to parameter of type 'number'，修复一下

提示优化后
请对 `transaction-history-dialog.tsx` 组件进行以下 7 项具体优化：

## 1. 样式统一

-   参考 `transaction-dialog.tsx` 的样式设计
-   使用相同的背景色 (`bg-card`)、间距、字体大小等视觉元素
-   确保与新的统一交易对话框保持一致的设计风格

## 2. 修复表格宽度溢出问题

-   解决当前表格宽度超出对话框宽度的布局问题
-   使用适当的容器和滚动机制确保表格在对话框内正确显示
-   考虑使用 `overflow-x-auto` 或调整列宽分配

## 3. 重新设计表格列结构

按以下 8 列重新组织表格：

-   **交易日期** - 显示交易的具体日期
-   **类型** - 显示交易类型（买入、卖出、合股、拆股、除权除息）
-   **成交价/数量** - 合并列，上下布局显示：上行显示成交价，下行显示数量
-   **税费/佣金** - 合并列，上下布局显示：上行显示税费，下行显示佣金
-   **成交金额** - 显示交易总金额
-   **说明** - 显示证券名称或其他说明信息
-   **备注** - 显示用户备注信息
-   **操作** - 显示编辑和删除按钮

## 4. 实现表格分页功能

-   当交易记录数量较多时，添加分页控件
-   建议每页显示 15-20 条记录
-   使用 shadcn-ui 的分页组件或自定义分页控件
-   在表格底部显示分页导航

## 5. 优化备注列显示

-   为备注列分配较宽的宽度（建议占表格宽度的 20-25%）
-   实现文字省略号显示（使用 `truncate` 或 `text-ellipsis`）
-   添加 Tooltip 组件，鼠标悬停时显示完整备注内容
-   确保长文本不会破坏表格布局

## 6. 调整对话框尺寸

-   增加对话框的宽度和高度，确保表格有足够的显示空间
-   建议最小宽度为 `max-w-6xl` 或 `max-w-7xl`
-   设置合适的最大高度（如 `max-h-[80vh]`）并支持内容滚动
-   确保在不同屏幕尺寸下都有良好的显示效果

## 7. 修复 transfers-table.tsx 类型错误

-   定位并修复 `transfers-table.tsx` 文件中的 TypeScript 类型错误
-   错误信息：`Argument of type 'string' is not assignable to parameter of type 'number'`
-   检查所有涉及数字类型参数的函数调用，确保传入正确的数据类型
-   必要时添加类型转换或更新类型定义

## 技术要求

-   保持现有的编辑和删除功能不变
-   确保所有修改都具有类型安全性
-   使用 shadcn-ui 组件库的设计规范
-   保持与系统整体风格的一致性
-   测试各种数据量情况下的显示效果
