07-18
1、先通读一下该项目，了解项目结构，所用技术栈，架构等等。
2、重置数据库，只保留 users 表，python 中对应的代码也要一并清理。
3、重新设计持仓管理相关表，并初始化数据库，具体关系如下
`投资组合表
{
"market": "CHA",
"name": "A 股",
"assets": 555487.24,
"principal": 400000.0,
"cash": 196584.74,
"sign": "¥",
"currency": "CNY",
"market_value": 358902.5,
"float_amount": 50149.28,
"float_rate": 0.1624,
"accum_amount": 155487.24,
"accum_rate": 0.3616,
"day_float_amount": 3901.1,
"day_float_rate": 0.0071
}
品种持仓表
{
"symbol": "SH513050",
"name": "中概互联网 ETF",
"shares": 22900.0,
"current": 1.42,
"change": 0.033,
"percentage": 2.38,
"currency": "CNY",
"diluted_cost": 1.4237,
"hold_cost": 1.4237,
"market_value": 32518.0,
"float_amount": -84.73,
"float_rate": -0.0026,
"accum_amount": -85.8,
"accum_rate": -0.0026,
"day_float_amount": 755.7,
"day_float_rate": 0.0238,
}
品种交易记录表
{
"symbol": "SH512980",
"name": "传媒 ETF",
"type": 2,
"time": 1710777600000,
"shares": 14300.0,
"price": 0.775,
"comment": "0.9 网，条件单卖出",
"commission": null,
"tax": null,
"commission_rate": null,
"tax_rate": null,
"unit_shares": null,
"unit_dividend": null,
"unit_increase_shares": null,
"record_date": null,
"type_name": "卖出",
"desc": "",
"amount": 11082.5
}
银证转账表
{
"type": 1, // 1:转入，2:转出
"market": "CHA",
"amount": 100000.0,
"time": 1709136000000,
"create_at": 1709196698954,
"update_at": 1709196698954
}
以上是四个表的基础字段

这四个表的关系如下
投资组合表和品种持仓表是一对多的关系，即一个投资组合可以有多个品种持仓
品种持仓表和品种交易记录表是一对多的关系，即一个品种持仓可以有多个交易记录
投资组合表和银证转账表是一对多的关系，即一个投资组合可以有多个银证转账记录
投资组合表和品种交易记录表是一对多的关系，这里等于冗余一个字段，方便查询，即通过组合 ID 可以查询到该组合下的所有交易记录`

4、步骤 3 完成后，基于对应的表结构完成 python 对应的模型和基本的 API 开发
5、以上步骤都完成后，根据最新的表结构设计，更新数据库初始化脚本和相关的文档
