@portfolio-page.tsx @portfolio-tabs.tsx 仔细阅读这两个文件的代码，我做了大体的控件布局，我需要你帮我以下功能
1、添加组合 这个按钮帮我实现这个完整的功能，新增组合只需要输入组合名称就可以，对应的 API 也要相应实现
2、实现组合页面 tab 的动态初始化，查询组合数据，初始化到 tab 组件中
3、组合添加完后，对应的 tab 需要同步更新

请参考 @/Users/<USER>/Work/Git/xGithub/trash-track/trash-track-web/src/app/(main)/portfolio/default/\_components/portfolio-tabs.tsx 里面的样式，我要求跟里面的一样，tab 不需要关闭按钮，然后名称不要叫 dynamic-portfolio-tabs.tsx，你直接在@/Users/<USER>/Work/Git/xGithub/trash-track/trash-track-web/src/app/(main)/portfolio/default/\_components/portfolio-tabs.tsx 文件中实现逻辑就好。

好的，目前看起来还不错，接下来帮我实现组合里面的持仓功能
1、界面参考@xueqiu/UI-持仓主界面.png
2、要求在@holding-tabs.tsx 文件下实现，样式要保持跟源工程整体风格一致。
3、当选中某个组合时，或动态获取该组合的持仓数据，初始化到 holding-tabs.tsx 组件中。当点击持仓页面下的交易记录，转账记录 tab 时，需要展示该组合下的交易记录和转账记录。
4、持仓品种数据，交易记录数据，转账记录数据，对应的 API 也要相应实现
5、买入，卖出，银证转账，三个按钮的功能要实现，先实现最基础的数据添加功能。
// 看起来这次输入太多了，没能全部完成

@holding-tabs.tsx 有几个地方需要优化和补充的
1、买入，卖出，银证转账功能要完善一下，目前点击按钮是无效的
2、表格不要放到 Card 组件里面，这部分优化一下，样式参考一下https://tweakcn.com/ 中 Tangerine 主题的 table
3、表格右侧需要添加 action 列，放置添加交易记录的按钮，记录删除按钮，还有记录查看按钮（查看该品种的交易记录，弹窗表格实现）

卖出 选择证券后报错了，错误在@/Users/<USER>/Work/Git/xGithub/trash-track/trash-track-web/src/app/(main)/portfolio/default/\_components/sell-stock-form.tsx 132 行 <span className="font-medium">¥{selectedHolding.hold_cost.toFixed(2)}</span> selectedHolding.hold_cost.toFixed is not a function 看起来是字符串类型直接 toFixed，但是我看定义是 number，为什么会出现这种情况，其他的再检查一下，有没有类似的错误，页一并修复了

json_encoders 已被标记为过时，未来可能被移除。使用 @field_serializer 或自定义序列化函数是 Pydantic v2 的推荐实践。 这部分是真实的吗，如果是真实的请你优化一下，要考虑多次重复配置时是否可以抽象出来，统一配置

@xxx-web 前端工程中目前有很多 eslint 报错，你需要修复一下

固定提示词
前端工程请严格按照配置的 eslint 规则来开发

后续改用 rule 来规范
