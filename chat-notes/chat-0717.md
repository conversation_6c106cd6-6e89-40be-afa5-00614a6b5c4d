1、先通读一下该项目，了解项目结构，所用技术栈，架构等等。
2、我发现有两个初始化数据库的地方，一个是 init.sql，一个是 create_tables.py，我希望只保留一种方式，你来决定用哪种方式，然后去掉另一种方式的代码，并且确保数据库可以正常初始化。
3、我发现数据库的 database 叫 index_investing，我希望改成 trash-track
4、检查 API 服务是否有提供 API 文档 Swagger UI 和 ReDoc，这部分不需要，直接删掉
5、完成后记得更新 README.md 文件

Swagger UI 和 ReDoc 相关依赖也要一起删除
重置了 admin 用户密码，那初始化数据库脚本中的密码也要改一下，不然每次初始化都无法登陆
你加了一些重制密码的脚本还有测试集成的脚本如果没用 了要记得删除

投资组合表
{
"market": "CHA",
"name": "A 股",
"assets": 555487.24,
"principal": 400000.0,
"cash": 196584.74,
"sign": "¥",
"currency": "CNY",
"market_value": 358902.5,
"float_amount": 50149.28,
"float_rate": 0.1624,
"accum_amount": 155487.24,
"accum_rate": 0.3616,
"day_float_amount": 3901.1,
"day_float_rate": 0.0071
}
品种持仓表
{
"symbol": "SH513050",
"name": "中概互联网 ETF",
"shares": 22900.0,
"current": 1.42,
"change": 0.033,
"percentage": 2.38,
"currency": "CNY",
"diluted_cost": 1.4237,
"hold_cost": 1.4237,
"market_value": 32518.0,
"float_amount": -84.73,
"float_rate": -0.0026,
"accum_amount": -85.8,
"accum_rate": -0.0026,
"day_float_amount": 755.7,
"day_float_rate": 0.0238,
}
品种交易记录表
{
"symbol": "SH512980",
"name": "传媒 ETF",
"type": 2,
"time": 1710777600000,
"shares": 14300.0,
"price": 0.775,
"comment": "0.9 网，条件单卖出",
"commission": null,
"tax": null,
"commission_rate": null,
"tax_rate": null,
"unit_shares": null,
"unit_dividend": null,
"unit_increase_shares": null,
"record_date": null,
"type_name": "卖出",
"desc": "",
"amount": 11082.5
}
银证转账表
{
"type": 1, // 1:转入，2:转出
"market": "CHA",
"amount": 100000.0,
"time": 1709136000000,
"create_at": 1709196698954,
"update_at": 1709196698954
}
以上是四个表的基础字段

这四个表的关系如下
投资组合表和品种持仓表是一对多的关系，即一个投资组合可以有多个品种持仓
品种持仓表和品种交易记录表是一对多的关系，即一个品种持仓可以有多个交易记录
投资组合表和银证转账表是一对多的关系，即一个投资组合可以有多个银证转账记录
投资组合表和品种交易记录表是一对多的关系，这里等于冗余一个字段，方便查询，即通过组合 ID 可以查询到该组合下的所有交易记录

1、基于上面的内容，帮我设计一下数据库表结构，帮给出数据库关系文档，方便后续开发使用
2、完成上面相关数据的 API 设计与实现
3、UI 上实现组合的管理，交易记录的管理，转账记录的管理，持仓的查看，UI 需要好好设计一下，要考虑到原来的项目风格，要考虑美观，易用性
4、持仓不需要 CRUD，它是通过相应的交易记录新增出来的，为了支持历史持仓查询，清仓品种不是删除记录，而是有一个状态来标识，这部分你再设计一下

