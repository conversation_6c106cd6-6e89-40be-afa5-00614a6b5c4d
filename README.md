# Trash Track

垃圾追踪管理系统 - 一个现代化的全栈 Web 应用，提供投资组合管理、交易记录、持仓分析等功能。

## 项目结构

```
trash-track/
├── trash-track-api/        # FastAPI后端服务
├── trash-track-web/        # Next.js前端应用
├── docker-compose.yml      # Docker编排配置
├── README.md              # 项目说明文档
└── xueqiu/               # 雪球数据文件
```

## 技术栈

### 后端 (trash-track-api)

-   **框架**: FastAPI
-   **数据库**: PostgreSQL
-   **缓存**: Redis
-   **认证**: JWT (JSON Web Tokens)
-   **ORM**: SQLAlchemy
-   **测试**: pytest

### 前端 (trash-track-web)

-   **框架**: Next.js 15
-   **UI 库**: React 18
-   **语言**: TypeScript
-   **样式**: Tailwind CSS
-   **组件库**: Shadcn/ui
-   **状态管理**: React Hooks
-   **表单处理**: React Hook Form + Zod

## 环境要求

-   Node.js 18.0+
-   Python 3.9+
-   PostgreSQL 13+
-   Redis 6+
-   Docker & Docker Compose (可选)

## 快速开始

### 使用 Docker Compose (推荐)

1. **克隆项目**

```bash
git clone <repository-url>
cd trash-track
```

2. **启动所有服务**

```bash
# 启动数据库和缓存服务
docker-compose up -d postgres redis

# 等待数据库启动完成后，启动后端和前端服务
docker-compose --profile production up -d
```

3. **访问应用**

-   前端应用: http://localhost:3000
-   后端 API: http://localhost:8000
-   数据库: localhost:5432 (用户名: postgres, 密码: postgres123)
-   Redis: localhost:6379

### 本地开发环境

#### 1. 启动基础服务

```bash
# 启动PostgreSQL和Redis
docker-compose up -d postgres redis
```

#### 2. 后端服务

```bash
cd trash-track-api

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
python start.py
# 或
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

#### 3. 前端服务

```bash
cd trash-track-web

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 配置说明

### 环境变量

#### 后端配置 (.env)

```
# 数据库配置
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/trash-track

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 跨域配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

#### 前端配置 (.env.local)

```
# API接口地址
NEXT_PUBLIC_API_URL=http://localhost:8000

# 应用配置
NEXT_PUBLIC_APP_NAME=垃圾追踪管理系统
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## 数据库初始化

数据库表会在 Docker 容器启动时自动通过 init.sql 脚本创建。如果需要手动初始化：

```bash
# 创建数据库
psql -U postgres
CREATE DATABASE trash-track;
\q

# 执行初始化脚本
psql -U postgres -d trash-track -f trash-track-api/init.sql
```

### 数据库表结构

系统包含以下核心表：

1. **users** - 用户表
2. **user_settings** - 用户配置表
3. **portfolios** - 投资组合表
4. **holdings** - 品种持仓表
5. **transactions** - 品种交易记录表
6. **bank_transfers** - 银证转账表

### 表关系

-   投资组合表和品种持仓表是一对多关系
-   品种持仓表和品种交易记录表通过 portfolio_id 关联
-   投资组合表和银证转账表是一对多关系
-   投资组合表和品种交易记录表是一对多关系

默认管理员账户：

-   用户名: admin
-   密码: secret

## 主要功能

### 认证系统

-   用户注册和登录
-   JWT 令牌认证
-   用户权限管理

### 投资组合管理

-   创建和管理投资组合
-   交易记录管理
-   持仓分析和统计
-   资金流水记录

### 数据分析

-   投资组合性能分析
-   持仓分布图表
-   收益率统计

## API 端点

### 认证相关

-   `POST /api/v1/auth/login` - 用户登录
-   `POST /api/v1/auth/logout` - 用户登出
-   `GET /api/v1/auth/me` - 获取当前用户信息

### 投资组合管理

-   `GET /api/v1/portfolios/` - 获取用户的投资组合列表
-   `POST /api/v1/portfolios/` - 创建新投资组合
-   `GET /api/v1/portfolios/{portfolio_id}` - 获取投资组合详情（包含持仓、交易记录、转账记录）
-   `PUT /api/v1/portfolios/{portfolio_id}` - 更新投资组合
-   `DELETE /api/v1/portfolios/{portfolio_id}` - 删除投资组合

### 持仓管理

-   `GET /api/v1/portfolios/{portfolio_id}/holdings` - 获取投资组合的持仓列表
-   `POST /api/v1/portfolios/{portfolio_id}/holdings` - 创建持仓
-   `PUT /api/v1/portfolios/{portfolio_id}/holdings/{holding_id}` - 更新持仓
-   `DELETE /api/v1/portfolios/{portfolio_id}/holdings/{holding_id}` - 删除持仓

### 交易记录

-   `GET /api/v1/portfolios/{portfolio_id}/transactions` - 获取交易记录列表
-   `POST /api/v1/portfolios/{portfolio_id}/transactions` - 创建交易记录
-   `PUT /api/v1/portfolios/{portfolio_id}/transactions/{transaction_id}` - 更新交易记录
-   `DELETE /api/v1/portfolios/{portfolio_id}/transactions/{transaction_id}` - 删除交易记录

### 银证转账

-   `GET /api/v1/portfolios/{portfolio_id}/bank-transfers` - 获取银证转账记录列表
-   `POST /api/v1/portfolios/{portfolio_id}/bank-transfers` - 创建银证转账记录
-   `PUT /api/v1/portfolios/{portfolio_id}/bank-transfers/{transfer_id}` - 更新银证转账记录
-   `DELETE /api/v1/portfolios/{portfolio_id}/bank-transfers/{transfer_id}` - 删除银证转账记录

### 批量导入

-   `POST /api/v1/portfolios/{portfolio_id}/import/holdings` - 批量导入持仓数据
-   `POST /api/v1/portfolios/{portfolio_id}/import/transactions` - 批量导入交易记录
-   `POST /api/v1/portfolios/{portfolio_id}/import/bank-transfers` - 批量导入银证转账记录

## 测试

### 后端测试

```bash
cd trash-track-api

# 运行单元测试
pytest

# 运行API测试
python test_api.py

# 运行投资组合API测试
python test_portfolio_api.py
```

### 前端测试

```bash
cd trash-track-web

# 运行测试
npm test
```

## 部署

### 生产环境部署

1. **构建和启动所有服务**

```bash
docker-compose --profile production up -d
```

2. **环境变量配置**
   确保在生产环境中设置正确的环境变量，特别是：

-   `SECRET_KEY`: 使用强密码
-   `DATABASE_URL`: 生产数据库连接
-   `CORS_ORIGINS`: 生产域名

### 性能优化建议

1. **数据库优化**

    - 使用连接池
    - 添加适当的索引
    - 定期备份数据

2. **缓存策略**

    - 使用 Redis 缓存频繁访问的数据
    - 实现 API 响应缓存

3. **前端优化**
    - 启用代码分割
    - 使用 CDN 加速静态资源
    - 实现图片懒加载

## 常见问题

### 数据库连接错误

-   检查 PostgreSQL 服务是否运行
-   验证数据库连接字符串是否正确
-   确认数据库用户有足够权限

### 前后端联调问题

-   检查 CORS 配置
-   验证 API 地址配置
-   确认端口没有冲突

### Docker 相关问题

-   确保 Docker 和 Docker Compose 已正确安装
-   检查端口是否被占用
-   查看容器日志排查问题

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

-   创建 Issue
-   发送邮件

---

© 2024 Trash Track. All rights reserved.
