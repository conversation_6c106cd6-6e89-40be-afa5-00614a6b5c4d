#!/usr/bin/env python3
"""
投资组合API测试脚本
测试新的投资组合管理API功能
"""

import requests
import json
from decimal import Decimal
from datetime import datetime
import time

# API基础URL
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

# 测试用户凭据
TEST_USER = {
    "username": "admin",
    "password": "secret"
}

class PortfolioAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.access_token = None
        self.portfolio_id = None
        
    def login(self):
        """用户登录获取访问令牌"""
        print("🔐 正在登录...")
        
        response = self.session.post(
            f"{API_BASE}/auth/login",
            data={
                "username": TEST_USER["username"],
                "password": TEST_USER["password"]
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            self.access_token = data["access_token"]
            self.session.headers.update({
                "Authorization": f"Bearer {self.access_token}"
            })
            print("✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return False
    
    def test_create_portfolio(self):
        """测试创建投资组合"""
        print("\n📊 测试创建投资组合...")
        
        portfolio_data = {
            "market": "CHA",
            "name": "测试投资组合",
            "assets": 100000.00,
            "principal": 100000.00,
            "cash": 50000.00,
            "sign": "¥",
            "currency": "CNY",
            "market_value": 50000.00,
            "float_amount": 0.00,
            "float_rate": 0.00,
            "accum_amount": 0.00,
            "accum_rate": 0.00,
            "day_float_amount": 0.00,
            "day_float_rate": 0.00
        }
        
        response = self.session.post(
            f"{API_BASE}/portfolios/",
            json=portfolio_data
        )
        
        if response.status_code == 200:
            data = response.json()
            self.portfolio_id = data["id"]
            print(f"✅ 投资组合创建成功，ID: {self.portfolio_id}")
            print(f"   名称: {data['name']}")
            print(f"   市场: {data['market']}")
            print(f"   总资产: {data['assets']}")
            return True
        else:
            print(f"❌ 创建投资组合失败: {response.status_code} - {response.text}")
            return False
    
    def test_get_portfolios(self):
        """测试获取投资组合列表"""
        print("\n📋 测试获取投资组合列表...")
        
        response = self.session.get(f"{API_BASE}/portfolios/")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取投资组合列表成功，共 {len(data)} 个组合")
            for portfolio in data:
                print(f"   - {portfolio['name']} ({portfolio['market']})")
            return True
        else:
            print(f"❌ 获取投资组合列表失败: {response.status_code} - {response.text}")
            return False
    
    def test_create_holding(self):
        """测试创建持仓"""
        print("\n💼 测试创建持仓...")
        
        if not self.portfolio_id:
            print("❌ 需要先创建投资组合")
            return False
        
        holding_data = {
            "symbol": "SH513050",
            "name": "中概互联网ETF",
            "shares": 10000.00,
            "current": 1.42,
            "change": 0.033,
            "percentage": 2.38,
            "currency": "CNY",
            "diluted_cost": 1.4237,
            "hold_cost": 1.4237,
            "market_value": 14200.00,
            "float_amount": 330.00,
            "float_rate": 2.38,
            "accum_amount": -37.00,
            "accum_rate": -0.26,
            "day_float_amount": 330.00,
            "day_float_rate": 2.38
        }
        
        response = self.session.post(
            f"{API_BASE}/portfolios/{self.portfolio_id}/holdings",
            json=holding_data
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 持仓创建成功")
            print(f"   证券: {data['symbol']} - {data['name']}")
            print(f"   持仓数量: {data['shares']}")
            print(f"   市值: {data['market_value']}")
            return True
        else:
            print(f"❌ 创建持仓失败: {response.status_code} - {response.text}")
            return False
    
    def test_create_transaction(self):
        """测试创建交易记录"""
        print("\n💰 测试创建交易记录...")
        
        if not self.portfolio_id:
            print("❌ 需要先创建投资组合")
            return False
        
        transaction_data = {
            "symbol": "SH513050",
            "name": "中概互联网ETF",
            "type": 1,  # 买入
            "time": int(time.time() * 1000),  # 当前时间戳（毫秒）
            "shares": 5000.00,
            "price": 1.42,
            "comment": "测试买入",
            "commission": 5.00,
            "tax": 0.00,
            "commission_rate": 0.0003,
            "tax_rate": 0.0000,
            "type_name": "买入",
            "desc": "测试交易",
            "amount": 7100.00
        }
        
        response = self.session.post(
            f"{API_BASE}/portfolios/{self.portfolio_id}/transactions",
            json=transaction_data
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 交易记录创建成功")
            print(f"   证券: {data['symbol']} - {data['name']}")
            print(f"   类型: {data['type_name']}")
            print(f"   数量: {data['shares']}")
            print(f"   价格: {data['price']}")
            print(f"   金额: {data['amount']}")
            return True
        else:
            print(f"❌ 创建交易记录失败: {response.status_code} - {response.text}")
            return False
    
    def test_create_bank_transfer(self):
        """测试创建银证转账记录"""
        print("\n🏦 测试创建银证转账记录...")
        
        if not self.portfolio_id:
            print("❌ 需要先创建投资组合")
            return False
        
        transfer_data = {
            "type": 1,  # 转入
            "market": "CHA",
            "amount": 50000.00,
            "time": int(time.time() * 1000)  # 当前时间戳（毫秒）
        }
        
        response = self.session.post(
            f"{API_BASE}/portfolios/{self.portfolio_id}/bank-transfers",
            json=transfer_data
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 银证转账记录创建成功")
            print(f"   类型: {'转入' if data['type'] == 1 else '转出'}")
            print(f"   市场: {data['market']}")
            print(f"   金额: {data['amount']}")
            return True
        else:
            print(f"❌ 创建银证转账记录失败: {response.status_code} - {response.text}")
            return False
    
    def test_get_portfolio_detail(self):
        """测试获取投资组合详情"""
        print("\n📊 测试获取投资组合详情...")
        
        if not self.portfolio_id:
            print("❌ 需要先创建投资组合")
            return False
        
        response = self.session.get(f"{API_BASE}/portfolios/{self.portfolio_id}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取投资组合详情成功")
            print(f"   名称: {data['name']}")
            print(f"   持仓数量: {len(data['holdings'])}")
            print(f"   交易记录数量: {len(data['transactions'])}")
            print(f"   转账记录数量: {len(data['bank_transfers'])}")
            return True
        else:
            print(f"❌ 获取投资组合详情失败: {response.status_code} - {response.text}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始投资组合API测试")
        print("=" * 50)
        
        # 登录
        if not self.login():
            return False
        
        # 运行测试
        tests = [
            self.test_create_portfolio,
            self.test_get_portfolios,
            self.test_create_holding,
            self.test_create_transaction,
            self.test_create_bank_transfer,
            self.test_get_portfolio_detail
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                time.sleep(0.5)  # 短暂延迟
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        print("\n" + "=" * 50)
        print(f"📊 测试完成: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！")
        else:
            print("⚠️  部分测试失败，请检查API实现")
        
        return passed == total


def main():
    """主函数"""
    print("投资组合管理系统 API 测试")
    print("确保API服务器正在运行在 http://localhost:8000")
    
    # 检查服务器是否可用
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ API服务器不可用")
            return
    except requests.exceptions.RequestException:
        print("❌ 无法连接到API服务器")
        return
    
    # 运行测试
    tester = PortfolioAPITester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
