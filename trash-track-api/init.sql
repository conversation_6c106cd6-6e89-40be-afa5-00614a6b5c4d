-- 数据库已经通过环境变量创建，直接使用
-- \c trash_track;

-- 删除现有表（如果存在）以重新创建使用BIGINT主键的表
DROP TABLE IF EXISTS bank_transfers CASCADE;
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS holdings CASCADE;
DROP TABLE IF EXISTS portfolios CASCADE;
DROP TABLE IF EXISTS user_settings CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户配置表
CREATE TABLE IF NOT EXISTS user_settings (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, setting_key)
);

-- 投资组合表
CREATE TABLE IF NOT EXISTS portfolios (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    market VARCHAR(10) NOT NULL, -- 'CHA', 'US', 'HK', 'ALL'
    name VARCHAR(100) NOT NULL,
    assets DECIMAL(15, 2) DEFAULT 0, -- 总资产
    principal DECIMAL(15, 2) DEFAULT 0, -- 本金
    cash DECIMAL(15, 2) DEFAULT 0, -- 现金
    sign VARCHAR(5) DEFAULT '¥', -- 货币符号
    currency VARCHAR(10) DEFAULT 'CNY', -- 货币代码
    market_value DECIMAL(15, 2) DEFAULT 0, -- 市值
    float_amount DECIMAL(15, 2) DEFAULT 0, -- 浮动金额
    float_rate DECIMAL(10, 6) DEFAULT 0, -- 浮动比例
    accum_amount DECIMAL(15, 2) DEFAULT 0, -- 累计收益金额
    accum_rate DECIMAL(10, 6) DEFAULT 0, -- 累计收益率
    day_float_amount DECIMAL(15, 2) DEFAULT 0, -- 日浮动金额
    day_float_rate DECIMAL(10, 6) DEFAULT 0, -- 日浮动比例
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 品种持仓表
CREATE TABLE IF NOT EXISTS holdings (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    portfolio_id BIGINT NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL, -- 证券代码，如 SH513050
    name VARCHAR(100) NOT NULL, -- 证券名称，如 中概互联网ETF
    shares DECIMAL(15, 2) NOT NULL DEFAULT 0, -- 持仓数量
    current DECIMAL(10, 4) DEFAULT 0, -- 当前价格
    change DECIMAL(10, 4) DEFAULT 0, -- 价格变动
    percentage DECIMAL(10, 6) DEFAULT 0, -- 涨跌幅
    currency VARCHAR(10) DEFAULT 'CNY', -- 货币
    diluted_cost DECIMAL(10, 4) DEFAULT 0, -- 摊薄成本
    hold_cost DECIMAL(10, 4) DEFAULT 0, -- 持仓成本
    market_value DECIMAL(15, 2) DEFAULT 0, -- 市值
    float_amount DECIMAL(15, 2) DEFAULT 0, -- 浮动金额
    float_rate DECIMAL(10, 6) DEFAULT 0, -- 浮动比例
    accum_amount DECIMAL(15, 2) DEFAULT 0, -- 累计收益金额
    accum_rate DECIMAL(10, 6) DEFAULT 0, -- 累计收益率
    day_float_amount DECIMAL(15, 2) DEFAULT 0, -- 日浮动金额
    day_float_rate DECIMAL(10, 6) DEFAULT 0, -- 日浮动比例
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(portfolio_id, symbol)
);

-- 品种交易记录表
CREATE TABLE IF NOT EXISTS transactions (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    portfolio_id BIGINT NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL, -- 证券代码
    name VARCHAR(100) NOT NULL, -- 证券名称
    type INTEGER NOT NULL, -- 交易类型：1=买入，2=卖出，3=分红，4=拆股，5=合股，6=除权除息
    time BIGINT NOT NULL, -- 交易时间戳（毫秒）
    shares DECIMAL(15, 2) NOT NULL, -- 交易数量
    price DECIMAL(10, 4) NOT NULL, -- 交易价格
    comment TEXT, -- 备注
    commission DECIMAL(10, 2), -- 佣金
    tax DECIMAL(10, 2), -- 税费
    commission_rate DECIMAL(10, 6), -- 佣金率
    tax_rate DECIMAL(10, 6), -- 税率
    unit_shares DECIMAL(15, 2), -- 单位股数（用于拆股合股）
    unit_dividend DECIMAL(10, 4), -- 单位分红
    unit_increase_shares DECIMAL(15, 2), -- 单位增股
    record_date BIGINT, -- 记录日期时间戳
    type_name VARCHAR(20), -- 交易类型名称
    description TEXT, -- 描述
    amount DECIMAL(15, 2) NOT NULL, -- 交易金额
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 银证转账表
CREATE TABLE IF NOT EXISTS bank_transfers (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    portfolio_id BIGINT NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    type INTEGER NOT NULL, -- 1=转入，2=转出
    market VARCHAR(10) NOT NULL, -- 市场代码，如 CHA
    amount DECIMAL(15, 2) NOT NULL, -- 转账金额
    time BIGINT NOT NULL, -- 转账时间戳（毫秒）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX IF NOT EXISTS idx_portfolios_market ON portfolios(market);
CREATE INDEX IF NOT EXISTS idx_holdings_portfolio_id ON holdings(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_holdings_symbol ON holdings(symbol);
CREATE INDEX IF NOT EXISTS idx_transactions_portfolio_id ON transactions(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_transactions_symbol ON transactions(symbol);
CREATE INDEX IF NOT EXISTS idx_transactions_time ON transactions(time);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_bank_transfers_portfolio_id ON bank_transfers(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_bank_transfers_type ON bank_transfers(type);
CREATE INDEX IF NOT EXISTS idx_bank_transfers_time ON bank_transfers(time);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON user_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_portfolios_updated_at BEFORE UPDATE ON portfolios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_holdings_updated_at BEFORE UPDATE ON holdings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bank_transfers_updated_at BEFORE UPDATE ON bank_transfers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认管理员用户 (密码: secret)
INSERT INTO users (username, email, password_hash, is_superuser)
VALUES ('admin', '<EMAIL>', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', TRUE)
ON CONFLICT (username) DO NOTHING;
