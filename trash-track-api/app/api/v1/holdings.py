from typing import List
from decimal import Decimal
import time
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...api.deps import get_current_user
from ...models.user import User
from ...models.portfolio import Portfolio, Holding, Transaction, BankTransfer
from ...schemas.holding import (
    HoldingResponse, HoldingCreate, HoldingUpdate,
    TransactionResponse, TransactionCreate, TransactionUpdate,
    BankTransferResponse, BankTransferCreate, BankTransferUpdate,
    BuyTransactionRequest, SellTransactionRequest, TransferRequest
)

router = APIRouter()


def get_portfolio_by_id(portfolio_id: int, user: User, db: Session) -> Portfolio:
    """获取用户的投资组合"""
    portfolio = db.query(Portfolio).filter(
        Portfolio.id == portfolio_id,
        Portfolio.user_id == user.id
    ).first()
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portfolio not found"
        )
    
    return portfolio


@router.get("/{portfolio_id}/holdings", response_model=List[HoldingResponse])
async def get_holdings(
    portfolio_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取投资组合的持仓列表"""
    portfolio = get_portfolio_by_id(portfolio_id, current_user, db)
    
    holdings = db.query(Holding).filter(
        Holding.portfolio_id == portfolio_id
    ).order_by(Holding.created_at.desc()).all()
    
    return holdings


@router.get("/{portfolio_id}/transactions", response_model=List[TransactionResponse])
async def get_transactions(
    portfolio_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取投资组合的交易记录"""
    portfolio = get_portfolio_by_id(portfolio_id, current_user, db)
    
    transactions = db.query(Transaction).filter(
        Transaction.portfolio_id == portfolio_id
    ).order_by(Transaction.time.desc()).all()
    
    return transactions


@router.get("/{portfolio_id}/transfers", response_model=List[BankTransferResponse])
async def get_transfers(
    portfolio_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取投资组合的转账记录"""
    portfolio = get_portfolio_by_id(portfolio_id, current_user, db)
    
    transfers = db.query(BankTransfer).filter(
        BankTransfer.portfolio_id == portfolio_id
    ).order_by(BankTransfer.time.desc()).all()
    
    return transfers


@router.post("/{portfolio_id}/buy", response_model=TransactionResponse)
async def buy_stock(
    portfolio_id: int,
    transaction_data: BuyTransactionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """买入股票"""
    portfolio = get_portfolio_by_id(portfolio_id, current_user, db)
    
    # 计算交易总金额
    total_amount = transaction_data.shares * transaction_data.price
    
    # 检查现金是否足够
    if portfolio.cash < total_amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Insufficient cash balance"
        )
    
    # 创建交易记录
    transaction = Transaction(
        portfolio_id=portfolio_id,
        symbol=transaction_data.symbol,
        name=transaction_data.name,
        type=1,  # 买入
        time=int(time.time() * 1000),  # 当前时间戳（毫秒）
        shares=transaction_data.shares,
        price=transaction_data.price,
        comment=transaction_data.comment,
        amount=total_amount
    )
    
    db.add(transaction)
    
    # 更新或创建持仓
    holding = db.query(Holding).filter(
        Holding.portfolio_id == portfolio_id,
        Holding.symbol == transaction_data.symbol
    ).first()
    
    if holding:
        # 更新现有持仓
        old_total_cost = holding.shares * holding.hold_cost
        new_total_cost = old_total_cost + total_amount
        new_total_shares = holding.shares + transaction_data.shares
        
        holding.shares = new_total_shares
        holding.hold_cost = new_total_cost / new_total_shares if new_total_shares > 0 else Decimal('0')
        holding.diluted_cost = holding.hold_cost
        holding.market_value = holding.shares * holding.current
    else:
        # 创建新持仓
        holding = Holding(
            portfolio_id=portfolio_id,
            symbol=transaction_data.symbol,
            name=transaction_data.name,
            shares=transaction_data.shares,
            hold_cost=transaction_data.price,
            diluted_cost=transaction_data.price,
            market_value=transaction_data.shares * transaction_data.price,
            currency=portfolio.currency
        )
        db.add(holding)
    
    # 更新投资组合现金
    portfolio.cash -= total_amount
    
    db.commit()
    db.refresh(transaction)
    
    return transaction


@router.post("/{portfolio_id}/sell", response_model=TransactionResponse)
async def sell_stock(
    portfolio_id: int,
    transaction_data: SellTransactionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """卖出股票"""
    portfolio = get_portfolio_by_id(portfolio_id, current_user, db)
    
    # 查找持仓
    holding = db.query(Holding).filter(
        Holding.portfolio_id == portfolio_id,
        Holding.symbol == transaction_data.symbol
    ).first()
    
    if not holding:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Holding not found"
        )
    
    if holding.shares < transaction_data.shares:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Insufficient shares to sell"
        )
    
    # 计算交易总金额
    total_amount = transaction_data.shares * transaction_data.price
    
    # 创建交易记录
    transaction = Transaction(
        portfolio_id=portfolio_id,
        symbol=transaction_data.symbol,
        name=holding.name,
        type=2,  # 卖出
        time=int(time.time() * 1000),
        shares=transaction_data.shares,
        price=transaction_data.price,
        comment=transaction_data.comment,
        amount=total_amount
    )
    
    db.add(transaction)
    
    # 更新持仓
    holding.shares -= transaction_data.shares
    if holding.shares == 0:
        # 如果全部卖出，删除持仓
        db.delete(holding)
    else:
        # 更新市值
        holding.market_value = holding.shares * holding.current
    
    # 更新投资组合现金
    portfolio.cash += total_amount
    
    db.commit()
    db.refresh(transaction)
    
    return transaction


@router.post("/{portfolio_id}/transfer", response_model=BankTransferResponse)
async def bank_transfer(
    portfolio_id: int,
    transfer_data: TransferRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """银证转账"""
    portfolio = get_portfolio_by_id(portfolio_id, current_user, db)
    
    # 如果是转出，检查现金是否足够
    if transfer_data.type == 2 and portfolio.cash < transfer_data.amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Insufficient cash balance"
        )
    
    # 创建转账记录
    transfer = BankTransfer(
        portfolio_id=portfolio_id,
        type=transfer_data.type,
        market=portfolio.market,
        amount=transfer_data.amount,
        time=int(time.time() * 1000)
    )
    
    db.add(transfer)
    
    # 更新投资组合现金
    if transfer_data.type == 1:  # 转入
        portfolio.cash += transfer_data.amount
        portfolio.principal += transfer_data.amount
        portfolio.assets += transfer_data.amount
    else:  # 转出
        portfolio.cash -= transfer_data.amount
        portfolio.principal -= transfer_data.amount
        portfolio.assets -= transfer_data.amount
    
    db.commit()
    db.refresh(transfer)
    
    return transfer


@router.delete("/portfolios/{portfolio_id}/holdings/{holding_id}")
async def delete_holding(
    portfolio_id: int,
    holding_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除持仓记录"""
    # 检查投资组合是否存在且属于当前用户
    portfolio = db.query(Portfolio).filter(
        Portfolio.id == portfolio_id,
        Portfolio.user_id == current_user.id
    ).first()

    if not portfolio:
        raise HTTPException(status_code=404, detail="Portfolio not found")

    # 检查持仓是否存在
    holding = db.query(Holding).filter(
        Holding.id == holding_id,
        Holding.portfolio_id == portfolio_id
    ).first()

    if not holding:
        raise HTTPException(status_code=404, detail="Holding not found")

    # 删除持仓
    db.delete(holding)
    db.commit()

    return {"message": "Holding deleted successfully"}
