from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List

from ...core.database import get_db
from ...schemas.portfolio import (
    Portfolio, PortfolioCreate, PortfolioUpdate, PortfolioDetail,
    Holding, HoldingCreate, HoldingUpdate,
    Transaction, TransactionCreate, TransactionUpdate,
    BankTransfer, BankTransferCreate, BankTransferUpdate,
    PortfolioImport, TransactionImport, BankTransferImport
)
from ...schemas.user import User
from ...services.portfolio_service import PortfolioService
from ...api.deps import get_current_user

router = APIRouter()


# Portfolio endpoints
@router.get("/", response_model=List[Portfolio])
async def get_portfolios(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户的投资组合列表"""
    portfolio_service = PortfolioService(db)
    return portfolio_service.get_user_portfolios(current_user.id, skip=skip, limit=limit)


@router.post("/", response_model=Portfolio)
async def create_portfolio(
    portfolio_create: PortfolioCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建投资组合"""
    portfolio_service = PortfolioService(db)
    return portfolio_service.create_portfolio(current_user.id, portfolio_create)


@router.get("/{portfolio_id}", response_model=PortfolioDetail)
async def get_portfolio(
    portfolio_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取投资组合详情"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )
    
    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此投资组合"
        )
    
    # 获取相关数据
    holdings = portfolio_service.get_portfolio_holdings(portfolio_id)
    transactions = portfolio_service.get_portfolio_transactions(portfolio_id)
    bank_transfers = portfolio_service.get_portfolio_bank_transfers(portfolio_id)
    
    return PortfolioDetail(
        **portfolio.__dict__,
        holdings=holdings,
        transactions=transactions,
        bank_transfers=bank_transfers
    )


@router.put("/{portfolio_id}", response_model=Portfolio)
async def update_portfolio(
    portfolio_id: int,
    portfolio_update: PortfolioUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新投资组合"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )
    
    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )
    
    updated_portfolio = portfolio_service.update_portfolio(portfolio_id, portfolio_update)
    return updated_portfolio


@router.delete("/{portfolio_id}")
async def delete_portfolio(
    portfolio_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除投资组合"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )
    
    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此投资组合"
        )
    
    success = portfolio_service.delete_portfolio(portfolio_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除投资组合失败"
        )
    
    return {"message": "投资组合删除成功"}


# Holdings endpoints
@router.get("/{portfolio_id}/holdings", response_model=List[Holding])
async def get_portfolio_holdings(
    portfolio_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取投资组合的持仓列表"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )
    
    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此投资组合"
        )
    
    return portfolio_service.get_portfolio_holdings(portfolio_id, skip=skip, limit=limit)


@router.post("/{portfolio_id}/holdings", response_model=Holding)
async def create_holding(
    portfolio_id: int,
    holding_create: HoldingCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建持仓"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )
    
    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )
    
    # 检查是否已存在相同symbol的持仓
    existing_holding = portfolio_service.get_holding_by_symbol(portfolio_id, holding_create.symbol)
    if existing_holding:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"证券 {holding_create.symbol} 的持仓已存在"
        )
    
    return portfolio_service.create_holding(portfolio_id, holding_create)


@router.put("/{portfolio_id}/holdings/{holding_id}", response_model=Holding)
async def update_holding(
    portfolio_id: int,
    holding_id: int,
    holding_update: HoldingUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新持仓"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )
    
    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )
    
    holding = portfolio_service.get_holding(holding_id)
    if not holding or holding.portfolio_id != portfolio_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="持仓不存在"
        )
    
    updated_holding = portfolio_service.update_holding(holding_id, holding_update)
    return updated_holding


@router.delete("/{portfolio_id}/holdings/{holding_id}")
async def delete_holding(
    portfolio_id: int,
    holding_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除持仓"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)
    
    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )
    
    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )
    
    holding = portfolio_service.get_holding(holding_id)
    if not holding or holding.portfolio_id != portfolio_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="持仓不存在"
        )
    
    success = portfolio_service.delete_holding(holding_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除持仓失败"
        )
    
    return {"message": "持仓删除成功"}


# Transactions endpoints
@router.get("/{portfolio_id}/transactions", response_model=List[Transaction])
async def get_portfolio_transactions(
    portfolio_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取投资组合的交易记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此投资组合"
        )

    return portfolio_service.get_portfolio_transactions(portfolio_id, skip=skip, limit=limit)


@router.post("/{portfolio_id}/transactions", response_model=Transaction)
async def create_transaction(
    portfolio_id: int,
    transaction_create: TransactionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建交易记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )

    return portfolio_service.create_transaction(portfolio_id, transaction_create)


@router.put("/{portfolio_id}/transactions/{transaction_id}", response_model=Transaction)
async def update_transaction(
    portfolio_id: int,
    transaction_id: int,
    transaction_update: TransactionUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新交易记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )

    transaction = portfolio_service.get_transaction(transaction_id)
    if not transaction or transaction.portfolio_id != portfolio_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="交易记录不存在"
        )

    updated_transaction = portfolio_service.update_transaction(transaction_id, transaction_update)
    return updated_transaction


@router.delete("/{portfolio_id}/transactions/{transaction_id}")
async def delete_transaction(
    portfolio_id: int,
    transaction_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除交易记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )

    transaction = portfolio_service.get_transaction(transaction_id)
    if not transaction or transaction.portfolio_id != portfolio_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="交易记录不存在"
        )

    success = portfolio_service.delete_transaction(transaction_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除交易记录失败"
        )

    return {"message": "交易记录删除成功"}


# Bank transfers endpoints
@router.get("/{portfolio_id}/bank-transfers", response_model=List[BankTransfer])
async def get_portfolio_bank_transfers(
    portfolio_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取投资组合的银证转账记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此投资组合"
        )

    return portfolio_service.get_portfolio_bank_transfers(portfolio_id, skip=skip, limit=limit)


@router.post("/{portfolio_id}/bank-transfers", response_model=BankTransfer)
async def create_bank_transfer(
    portfolio_id: int,
    transfer_create: BankTransferCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建银证转账记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )

    return portfolio_service.create_bank_transfer(portfolio_id, transfer_create)


@router.put("/{portfolio_id}/bank-transfers/{transfer_id}", response_model=BankTransfer)
async def update_bank_transfer(
    portfolio_id: int,
    transfer_id: int,
    transfer_update: BankTransferUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新银证转账记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )

    transfer = portfolio_service.get_bank_transfer(transfer_id)
    if not transfer or transfer.portfolio_id != portfolio_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="银证转账记录不存在"
        )

    updated_transfer = portfolio_service.update_bank_transfer(transfer_id, transfer_update)
    return updated_transfer


@router.delete("/{portfolio_id}/bank-transfers/{transfer_id}")
async def delete_bank_transfer(
    portfolio_id: int,
    transfer_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除银证转账记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )

    transfer = portfolio_service.get_bank_transfer(transfer_id)
    if not transfer or transfer.portfolio_id != portfolio_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="银证转账记录不存在"
        )

    success = portfolio_service.delete_bank_transfer(transfer_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除银证转账记录失败"
        )

    return {"message": "银证转账记录删除成功"}


# Batch import endpoints
@router.post("/{portfolio_id}/import/holdings", response_model=List[Holding])
async def import_holdings(
    portfolio_id: int,
    holdings_data: List[HoldingCreate],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """批量导入持仓数据"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )

    return portfolio_service.create_holdings_batch(portfolio_id, holdings_data)


@router.post("/{portfolio_id}/import/transactions", response_model=List[Transaction])
async def import_transactions(
    portfolio_id: int,
    transactions_data: List[TransactionCreate],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """批量导入交易记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )

    return portfolio_service.create_transactions_batch(portfolio_id, transactions_data)


@router.post("/{portfolio_id}/import/bank-transfers", response_model=List[BankTransfer])
async def import_bank_transfers(
    portfolio_id: int,
    transfers_data: List[BankTransferCreate],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """批量导入银证转账记录"""
    portfolio_service = PortfolioService(db)
    portfolio = portfolio_service.get_portfolio(portfolio_id)

    if not portfolio:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="投资组合不存在"
        )

    # 检查权限
    if portfolio.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此投资组合"
        )

    return portfolio_service.create_bank_transfers_batch(portfolio_id, transfers_data)
