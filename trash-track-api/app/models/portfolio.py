from sqlalchemy import Column, String, Integer, Numeric, BigInteger, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base


class Portfolio(Base):
    """投资组合表"""
    __tablename__ = "portfolios"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False, index=True)
    market = Column(String(10), nullable=False, index=True)  # 'CHA', 'US', 'HK', 'ALL'
    name = Column(String(100), nullable=False)
    assets = Column(Numeric(15, 2), default=0)  # 总资产
    principal = Column(Numeric(15, 2), default=0)  # 本金
    cash = Column(Numeric(15, 2), default=0)  # 现金
    sign = Column(String(5), default='¥')  # 货币符号
    currency = Column(String(10), default='CNY')  # 货币代码
    market_value = Column(Numeric(15, 2), default=0)  # 市值
    float_amount = Column(Numeric(15, 2), default=0)  # 浮动金额
    float_rate = Column(Numeric(10, 6), default=0)  # 浮动比例
    accum_amount = Column(Numeric(15, 2), default=0)  # 累计收益金额
    accum_rate = Column(Numeric(10, 6), default=0)  # 累计收益率
    day_float_amount = Column(Numeric(15, 2), default=0)  # 日浮动金额
    day_float_rate = Column(Numeric(10, 6), default=0)  # 日浮动比例
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="portfolios")
    holdings = relationship("Holding", back_populates="portfolio", cascade="all, delete-orphan")
    transactions = relationship("Transaction", back_populates="portfolio", cascade="all, delete-orphan")
    bank_transfers = relationship("BankTransfer", back_populates="portfolio", cascade="all, delete-orphan")


class Holding(Base):
    """品种持仓表"""
    __tablename__ = "holdings"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    portfolio_id = Column(BigInteger, ForeignKey("portfolios.id"), nullable=False, index=True)
    symbol = Column(String(20), nullable=False, index=True)  # 证券代码，如 SH513050
    name = Column(String(100), nullable=False)  # 证券名称，如 中概互联网ETF
    shares = Column(Numeric(15, 2), nullable=False, default=0)  # 持仓数量
    current = Column(Numeric(10, 4), default=0)  # 当前价格
    change = Column(Numeric(10, 4), default=0)  # 价格变动
    percentage = Column(Numeric(10, 6), default=0)  # 涨跌幅
    currency = Column(String(10), default='CNY')  # 货币
    diluted_cost = Column(Numeric(10, 4), default=0)  # 摊薄成本
    hold_cost = Column(Numeric(10, 4), default=0)  # 持仓成本
    market_value = Column(Numeric(15, 2), default=0)  # 市值
    float_amount = Column(Numeric(15, 2), default=0)  # 浮动金额
    float_rate = Column(Numeric(10, 6), default=0)  # 浮动比例
    accum_amount = Column(Numeric(15, 2), default=0)  # 累计收益金额
    accum_rate = Column(Numeric(10, 6), default=0)  # 累计收益率
    day_float_amount = Column(Numeric(15, 2), default=0)  # 日浮动金额
    day_float_rate = Column(Numeric(10, 6), default=0)  # 日浮动比例
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    portfolio = relationship("Portfolio", back_populates="holdings")


class Transaction(Base):
    """品种交易记录表"""
    __tablename__ = "transactions"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    portfolio_id = Column(BigInteger, ForeignKey("portfolios.id"), nullable=False, index=True)
    symbol = Column(String(20), nullable=False, index=True)  # 证券代码
    name = Column(String(100), nullable=False)  # 证券名称
    type = Column(Integer, nullable=False, index=True)  # 交易类型：1=买入，2=卖出，3=分红，4=拆股，5=合股，6=除权除息
    time = Column(BigInteger, nullable=False, index=True)  # 交易时间戳（毫秒）
    shares = Column(Numeric(15, 2), nullable=False)  # 交易数量
    price = Column(Numeric(10, 4), nullable=False)  # 交易价格
    comment = Column(Text)  # 备注
    commission = Column(Numeric(10, 2))  # 佣金
    tax = Column(Numeric(10, 2))  # 税费
    commission_rate = Column(Numeric(10, 6))  # 佣金率
    tax_rate = Column(Numeric(10, 6))  # 税率
    unit_shares = Column(Numeric(15, 2))  # 单位股数（用于拆股合股）
    unit_dividend = Column(Numeric(10, 4))  # 单位分红
    unit_increase_shares = Column(Numeric(15, 2))  # 单位增股
    record_date = Column(BigInteger)  # 记录日期时间戳
    type_name = Column(String(20))  # 交易类型名称
    description = Column(Text)  # 描述
    amount = Column(Numeric(15, 2), nullable=False)  # 交易金额
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    portfolio = relationship("Portfolio", back_populates="transactions")


class BankTransfer(Base):
    """银证转账表"""
    __tablename__ = "bank_transfers"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    portfolio_id = Column(BigInteger, ForeignKey("portfolios.id"), nullable=False, index=True)
    type = Column(Integer, nullable=False, index=True)  # 1=转入，2=转出
    market = Column(String(10), nullable=False)  # 市场代码，如 CHA
    amount = Column(Numeric(15, 2), nullable=False)  # 转账金额
    time = Column(BigInteger, nullable=False, index=True)  # 转账时间戳（毫秒）
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    portfolio = relationship("Portfolio", back_populates="bank_transfers")
