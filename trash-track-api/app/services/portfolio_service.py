from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc
from typing import List, Optional
from decimal import Decimal

from ..models.portfolio import Portfolio, Holding, Transaction, BankTransfer
from ..schemas.portfolio import (
    PortfolioCreate, PortfolioUpdate,
    HoldingCreate, HoldingUpdate,
    TransactionCreate, TransactionUpdate,
    BankTransferCreate, BankTransferUpdate
)


class PortfolioService:
    def __init__(self, db: Session):
        self.db = db

    # Portfolio CRUD operations
    def get_portfolio(self, portfolio_id: int) -> Optional[Portfolio]:
        """获取投资组合"""
        return self.db.query(Portfolio).filter(Portfolio.id == portfolio_id).first()

    def get_user_portfolios(self, user_id: int, skip: int = 0, limit: int = 100) -> List[Portfolio]:
        """获取用户的投资组合列表"""
        return self.db.query(Portfolio).filter(
            Portfolio.user_id == user_id
        ).offset(skip).limit(limit).all()

    def create_portfolio(self, user_id: int, portfolio_create: PortfolioCreate) -> Portfolio:
        """创建投资组合"""
        db_portfolio = Portfolio(
            user_id=user_id,
            **portfolio_create.model_dump()
        )
        
        self.db.add(db_portfolio)
        self.db.commit()
        self.db.refresh(db_portfolio)
        return db_portfolio

    def update_portfolio(self, portfolio_id: int, portfolio_update: PortfolioUpdate) -> Optional[Portfolio]:
        """更新投资组合"""
        db_portfolio = self.get_portfolio(portfolio_id)
        if not db_portfolio:
            return None
        
        update_data = portfolio_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_portfolio, field, value)
        
        self.db.commit()
        self.db.refresh(db_portfolio)
        return db_portfolio

    def delete_portfolio(self, portfolio_id: int) -> bool:
        """删除投资组合"""
        db_portfolio = self.get_portfolio(portfolio_id)
        if not db_portfolio:
            return False
        
        self.db.delete(db_portfolio)
        self.db.commit()
        return True

    # Holding CRUD operations
    def get_holding(self, holding_id: int) -> Optional[Holding]:
        """获取持仓"""
        return self.db.query(Holding).filter(Holding.id == holding_id).first()

    def get_portfolio_holdings(self, portfolio_id: int, skip: int = 0, limit: int = 100) -> List[Holding]:
        """获取投资组合的持仓列表"""
        return self.db.query(Holding).filter(
            Holding.portfolio_id == portfolio_id
        ).offset(skip).limit(limit).all()

    def get_holding_by_symbol(self, portfolio_id: int, symbol: str) -> Optional[Holding]:
        """根据证券代码获取持仓"""
        return self.db.query(Holding).filter(
            and_(Holding.portfolio_id == portfolio_id, Holding.symbol == symbol)
        ).first()

    def create_holding(self, portfolio_id: int, holding_create: HoldingCreate) -> Holding:
        """创建持仓"""
        db_holding = Holding(
            portfolio_id=portfolio_id,
            **holding_create.model_dump()
        )
        
        self.db.add(db_holding)
        self.db.commit()
        self.db.refresh(db_holding)
        return db_holding

    def update_holding(self, holding_id: int, holding_update: HoldingUpdate) -> Optional[Holding]:
        """更新持仓"""
        db_holding = self.get_holding(holding_id)
        if not db_holding:
            return None
        
        update_data = holding_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_holding, field, value)
        
        self.db.commit()
        self.db.refresh(db_holding)
        return db_holding

    def delete_holding(self, holding_id: int) -> bool:
        """删除持仓"""
        db_holding = self.get_holding(holding_id)
        if not db_holding:
            return False
        
        self.db.delete(db_holding)
        self.db.commit()
        return True

    # Transaction CRUD operations
    def get_transaction(self, transaction_id: int) -> Optional[Transaction]:
        """获取交易记录"""
        return self.db.query(Transaction).filter(Transaction.id == transaction_id).first()

    def get_portfolio_transactions(self, portfolio_id: int, skip: int = 0, limit: int = 100) -> List[Transaction]:
        """获取投资组合的交易记录"""
        return self.db.query(Transaction).filter(
            Transaction.portfolio_id == portfolio_id
        ).order_by(desc(Transaction.time)).offset(skip).limit(limit).all()

    def create_transaction(self, portfolio_id: int, transaction_create: TransactionCreate) -> Transaction:
        """创建交易记录"""
        db_transaction = Transaction(
            portfolio_id=portfolio_id,
            **transaction_create.model_dump()
        )
        
        self.db.add(db_transaction)
        self.db.commit()
        self.db.refresh(db_transaction)
        return db_transaction

    def update_transaction(self, transaction_id: int, transaction_update: TransactionUpdate) -> Optional[Transaction]:
        """更新交易记录"""
        db_transaction = self.get_transaction(transaction_id)
        if not db_transaction:
            return None
        
        update_data = transaction_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_transaction, field, value)
        
        self.db.commit()
        self.db.refresh(db_transaction)
        return db_transaction

    def delete_transaction(self, transaction_id: int) -> bool:
        """删除交易记录"""
        db_transaction = self.get_transaction(transaction_id)
        if not db_transaction:
            return False
        
        self.db.delete(db_transaction)
        self.db.commit()
        return True

    # BankTransfer CRUD operations
    def get_bank_transfer(self, transfer_id: int) -> Optional[BankTransfer]:
        """获取银证转账记录"""
        return self.db.query(BankTransfer).filter(BankTransfer.id == transfer_id).first()

    def get_portfolio_bank_transfers(self, portfolio_id: int, skip: int = 0, limit: int = 100) -> List[BankTransfer]:
        """获取投资组合的银证转账记录"""
        return self.db.query(BankTransfer).filter(
            BankTransfer.portfolio_id == portfolio_id
        ).order_by(desc(BankTransfer.time)).offset(skip).limit(limit).all()

    def create_bank_transfer(self, portfolio_id: int, transfer_create: BankTransferCreate) -> BankTransfer:
        """创建银证转账记录"""
        db_transfer = BankTransfer(
            portfolio_id=portfolio_id,
            **transfer_create.model_dump()
        )
        
        self.db.add(db_transfer)
        self.db.commit()
        self.db.refresh(db_transfer)
        return db_transfer

    def update_bank_transfer(self, transfer_id: int, transfer_update: BankTransferUpdate) -> Optional[BankTransfer]:
        """更新银证转账记录"""
        db_transfer = self.get_bank_transfer(transfer_id)
        if not db_transfer:
            return None
        
        update_data = transfer_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_transfer, field, value)
        
        self.db.commit()
        self.db.refresh(db_transfer)
        return db_transfer

    def delete_bank_transfer(self, transfer_id: int) -> bool:
        """删除银证转账记录"""
        db_transfer = self.get_bank_transfer(transfer_id)
        if not db_transfer:
            return False
        
        self.db.delete(db_transfer)
        self.db.commit()
        return True

    # Batch operations
    def create_holdings_batch(self, portfolio_id: int, holdings_data: List[HoldingCreate]) -> List[Holding]:
        """批量创建持仓"""
        db_holdings = []
        for holding_data in holdings_data:
            # 检查是否已存在相同symbol的持仓
            existing = self.get_holding_by_symbol(portfolio_id, holding_data.symbol)
            if existing:
                # 更新现有持仓
                update_data = holding_data.model_dump()
                for field, value in update_data.items():
                    setattr(existing, field, value)
                db_holdings.append(existing)
            else:
                # 创建新持仓
                db_holding = Holding(
                    portfolio_id=portfolio_id,
                    **holding_data.model_dump()
                )
                self.db.add(db_holding)
                db_holdings.append(db_holding)
        
        self.db.commit()
        for holding in db_holdings:
            self.db.refresh(holding)
        return db_holdings

    def create_transactions_batch(self, portfolio_id: int, transactions_data: List[TransactionCreate]) -> List[Transaction]:
        """批量创建交易记录"""
        db_transactions = []
        for transaction_data in transactions_data:
            db_transaction = Transaction(
                portfolio_id=portfolio_id,
                **transaction_data.model_dump()
            )
            self.db.add(db_transaction)
            db_transactions.append(db_transaction)
        
        self.db.commit()
        for transaction in db_transactions:
            self.db.refresh(transaction)
        return db_transactions

    def create_bank_transfers_batch(self, portfolio_id: int, transfers_data: List[BankTransferCreate]) -> List[BankTransfer]:
        """批量创建银证转账记录"""
        db_transfers = []
        for transfer_data in transfers_data:
            db_transfer = BankTransfer(
                portfolio_id=portfolio_id,
                **transfer_data.model_dump()
            )
            self.db.add(db_transfer)
            db_transfers.append(db_transfer)
        
        self.db.commit()
        for transfer in db_transfers:
            self.db.refresh(transfer)
        return db_transfers
