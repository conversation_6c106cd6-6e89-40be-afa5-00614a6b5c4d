from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from .base import BaseResponseModel


# Portfolio schemas
class PortfolioBase(BaseModel):
    market: str
    name: str
    assets: Optional[Decimal] = Decimal('0')
    principal: Optional[Decimal] = Decimal('0')
    cash: Optional[Decimal] = Decimal('0')
    sign: Optional[str] = '¥'
    currency: Optional[str] = 'CNY'
    market_value: Optional[Decimal] = Decimal('0')
    float_amount: Optional[Decimal] = Decimal('0')
    float_rate: Optional[Decimal] = Decimal('0')
    accum_amount: Optional[Decimal] = Decimal('0')
    accum_rate: Optional[Decimal] = Decimal('0')
    day_float_amount: Optional[Decimal] = Decimal('0')
    day_float_rate: Optional[Decimal] = Decimal('0')


class PortfolioCreate(PortfolioBase):
    pass


class PortfolioUpdate(BaseModel):
    market: Optional[str] = None
    name: Optional[str] = None
    assets: Optional[Decimal] = None
    principal: Optional[Decimal] = None
    cash: Optional[Decimal] = None
    sign: Optional[str] = None
    currency: Optional[str] = None
    market_value: Optional[Decimal] = None
    float_amount: Optional[Decimal] = None
    float_rate: Optional[Decimal] = None
    accum_amount: Optional[Decimal] = None
    accum_rate: Optional[Decimal] = None
    day_float_amount: Optional[Decimal] = None
    day_float_rate: Optional[Decimal] = None


class Portfolio(PortfolioBase, BaseResponseModel):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime


# Holding schemas
class HoldingBase(BaseModel):
    symbol: str
    name: str
    shares: Decimal
    current: Optional[Decimal] = Decimal('0')
    change: Optional[Decimal] = Decimal('0')
    percentage: Optional[Decimal] = Decimal('0')
    currency: Optional[str] = 'CNY'
    diluted_cost: Optional[Decimal] = Decimal('0')
    hold_cost: Optional[Decimal] = Decimal('0')
    market_value: Optional[Decimal] = Decimal('0')
    float_amount: Optional[Decimal] = Decimal('0')
    float_rate: Optional[Decimal] = Decimal('0')
    accum_amount: Optional[Decimal] = Decimal('0')
    accum_rate: Optional[Decimal] = Decimal('0')
    day_float_amount: Optional[Decimal] = Decimal('0')
    day_float_rate: Optional[Decimal] = Decimal('0')


class HoldingCreate(HoldingBase):
    pass


class HoldingUpdate(BaseModel):
    symbol: Optional[str] = None
    name: Optional[str] = None
    shares: Optional[Decimal] = None
    current: Optional[Decimal] = None
    change: Optional[Decimal] = None
    percentage: Optional[Decimal] = None
    currency: Optional[str] = None
    diluted_cost: Optional[Decimal] = None
    hold_cost: Optional[Decimal] = None
    market_value: Optional[Decimal] = None
    float_amount: Optional[Decimal] = None
    float_rate: Optional[Decimal] = None
    accum_amount: Optional[Decimal] = None
    accum_rate: Optional[Decimal] = None
    day_float_amount: Optional[Decimal] = None
    day_float_rate: Optional[Decimal] = None


class Holding(HoldingBase, BaseResponseModel):
    id: int
    portfolio_id: int
    created_at: datetime
    updated_at: datetime


# Transaction schemas
class TransactionBase(BaseModel):
    symbol: str
    name: str
    type: int  # 1=买入，2=卖出，3=分红，4=拆股，5=合股，6=除权除息
    time: int  # 时间戳（毫秒）
    shares: Decimal
    price: Decimal
    comment: Optional[str] = None
    commission: Optional[Decimal] = None
    tax: Optional[Decimal] = None
    commission_rate: Optional[Decimal] = None
    tax_rate: Optional[Decimal] = None
    unit_shares: Optional[Decimal] = None
    unit_dividend: Optional[Decimal] = None
    unit_increase_shares: Optional[Decimal] = None
    record_date: Optional[int] = None
    type_name: Optional[str] = None
    description: Optional[str] = None
    amount: Decimal


class TransactionCreate(TransactionBase):
    pass


class TransactionUpdate(BaseModel):
    symbol: Optional[str] = None
    name: Optional[str] = None
    type: Optional[int] = None
    time: Optional[int] = None
    shares: Optional[Decimal] = None
    price: Optional[Decimal] = None
    comment: Optional[str] = None
    commission: Optional[Decimal] = None
    tax: Optional[Decimal] = None
    commission_rate: Optional[Decimal] = None
    tax_rate: Optional[Decimal] = None
    unit_shares: Optional[Decimal] = None
    unit_dividend: Optional[Decimal] = None
    unit_increase_shares: Optional[Decimal] = None
    record_date: Optional[int] = None
    type_name: Optional[str] = None
    description: Optional[str] = None
    amount: Optional[Decimal] = None


class Transaction(TransactionBase, BaseResponseModel):
    id: int
    portfolio_id: int
    created_at: datetime
    updated_at: datetime


# BankTransfer schemas
class BankTransferBase(BaseModel):
    type: int  # 1=转入，2=转出
    market: str
    amount: Decimal
    time: int  # 时间戳（毫秒）


class BankTransferCreate(BankTransferBase):
    pass


class BankTransferUpdate(BaseModel):
    type: Optional[int] = None
    market: Optional[str] = None
    amount: Optional[Decimal] = None
    time: Optional[int] = None


class BankTransfer(BankTransferBase, BaseResponseModel):
    id: int
    portfolio_id: int
    created_at: datetime
    updated_at: datetime


# Portfolio detail with related data
class PortfolioDetail(Portfolio):
    holdings: List[Holding] = []
    transactions: List[Transaction] = []
    bank_transfers: List[BankTransfer] = []


# Batch import schemas
class PortfolioImport(BaseModel):
    """雪球组合数据导入格式"""
    market: str
    name: str
    assets: Decimal
    principal: Decimal
    cash: Decimal
    sign: str
    currency: str
    market_value: Decimal
    float_amount: Decimal
    float_rate: Decimal
    accum_amount: Decimal
    accum_rate: Decimal
    day_float_amount: Decimal
    day_float_rate: Decimal
    holdings: List[HoldingCreate] = []


class TransactionImport(BaseModel):
    """雪球交易记录导入格式"""
    transactions: List[TransactionCreate]


class BankTransferImport(BaseModel):
    """雪球银证转账导入格式"""
    bank_transfers: List[BankTransferCreate]
