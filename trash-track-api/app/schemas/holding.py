from decimal import Decimal
from typing import Optional
from datetime import datetime
from pydantic import BaseModel
from .base import BaseResponseModel


class HoldingBase(BaseModel):
    symbol: str
    name: str
    shares: Decimal
    current: Optional[Decimal] = Decimal('0')
    change: Optional[Decimal] = Decimal('0')
    percentage: Optional[Decimal] = Decimal('0')
    currency: Optional[str] = 'CNY'
    diluted_cost: Optional[Decimal] = Decimal('0')
    hold_cost: Optional[Decimal] = Decimal('0')
    market_value: Optional[Decimal] = Decimal('0')
    float_amount: Optional[Decimal] = Decimal('0')
    float_rate: Optional[Decimal] = Decimal('0')
    accum_amount: Optional[Decimal] = Decimal('0')
    accum_rate: Optional[Decimal] = Decimal('0')
    day_float_amount: Optional[Decimal] = Decimal('0')
    day_float_rate: Optional[Decimal] = Decimal('0')


class HoldingCreate(HoldingBase):
    portfolio_id: int


class HoldingUpdate(BaseModel):
    symbol: Optional[str] = None
    name: Optional[str] = None
    shares: Optional[Decimal] = None
    current: Optional[Decimal] = None
    change: Optional[Decimal] = None
    percentage: Optional[Decimal] = None
    currency: Optional[str] = None
    diluted_cost: Optional[Decimal] = None
    hold_cost: Optional[Decimal] = None
    market_value: Optional[Decimal] = None
    float_amount: Optional[Decimal] = None
    float_rate: Optional[Decimal] = None
    accum_amount: Optional[Decimal] = None
    accum_rate: Optional[Decimal] = None
    day_float_amount: Optional[Decimal] = None
    day_float_rate: Optional[Decimal] = None


class HoldingResponse(HoldingBase, BaseResponseModel):
    id: int
    portfolio_id: int
    created_at: datetime
    updated_at: datetime


class TransactionBase(BaseModel):
    symbol: str
    name: str
    type: int  # 1=买入，2=卖出，3=分红，4=拆股，5=合股，6=除权除息
    time: int  # 时间戳（毫秒）
    shares: Decimal
    price: Decimal
    comment: Optional[str] = None
    commission: Optional[Decimal] = Decimal('0')
    tax: Optional[Decimal] = Decimal('0')
    commission_rate: Optional[Decimal] = Decimal('0')
    tax_rate: Optional[Decimal] = Decimal('0')
    unit_shares: Optional[Decimal] = Decimal('0')
    unit_dividend: Optional[Decimal] = Decimal('0')
    unit_increase_shares: Optional[Decimal] = Decimal('0')
    record_date: Optional[int] = None
    type_name: Optional[str] = None
    description: Optional[str] = None
    amount: Decimal


class TransactionCreate(TransactionBase):
    portfolio_id: int


class TransactionUpdate(BaseModel):
    symbol: Optional[str] = None
    name: Optional[str] = None
    type: Optional[int] = None
    time: Optional[int] = None
    shares: Optional[Decimal] = None
    price: Optional[Decimal] = None
    comment: Optional[str] = None
    commission: Optional[Decimal] = None
    tax: Optional[Decimal] = None
    amount: Optional[Decimal] = None


class TransactionResponse(TransactionBase, BaseResponseModel):
    id: int
    portfolio_id: int
    created_at: datetime
    updated_at: datetime


class BankTransferBase(BaseModel):
    type: int  # 1=转入，2=转出
    market: str
    amount: Decimal
    time: int  # 时间戳（毫秒）


class BankTransferCreate(BankTransferBase):
    portfolio_id: int


class BankTransferUpdate(BaseModel):
    type: Optional[int] = None
    market: Optional[str] = None
    amount: Optional[Decimal] = None
    time: Optional[int] = None


class BankTransferResponse(BankTransferBase, BaseResponseModel):
    id: int
    portfolio_id: int
    created_at: datetime
    updated_at: datetime


# 用于买入/卖出操作的简化schemas
class BuyTransactionRequest(BaseModel):
    symbol: str
    name: str
    shares: Decimal
    price: Decimal
    comment: Optional[str] = None


class SellTransactionRequest(BaseModel):
    symbol: str
    shares: Decimal
    price: Decimal
    comment: Optional[str] = None


class TransferRequest(BaseModel):
    type: int  # 1=转入，2=转出
    amount: Decimal
    comment: Optional[str] = None
