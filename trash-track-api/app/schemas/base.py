from decimal import Decimal
from typing import Any
from pydantic import BaseModel, ConfigDict, model_serializer


class BaseResponseModel(BaseModel):
    """Base model for all response schemas with common configuration"""

    model_config = ConfigDict(
        from_attributes=True,
    )

    @model_serializer
    def serialize_model(self) -> dict[str, Any]:
        """Custom model serializer to convert Decimal to float"""
        data = {}
        for field_name, field_value in self.__dict__.items():
            if isinstance(field_value, Decimal):
                data[field_name] = float(field_value)
            else:
                data[field_name] = field_value
        return data
