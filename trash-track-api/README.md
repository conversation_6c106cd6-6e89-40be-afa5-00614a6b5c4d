# Trash Track API

垃圾追踪管理系统的后端 API 服务，提供用户认证、投资组合管理、交易记录、持仓分析等功能。

## 技术栈

-   **框架**: FastAPI
-   **数据库**: PostgreSQL
-   **缓存**: Redis
-   **认证**: JWT (JSON Web Tokens)
-   **ORM**: SQLAlchemy

-   **测试**: pytest

## 环境要求

-   Python 3.9+
-   PostgreSQL 13+
-   Redis 6+

## 依赖安装

### 1. 创建并激活虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Linux/macOS)
source venv/bin/activate

# 激活虚拟环境 (Windows)
venv\Scripts\activate
```

### 2. 安装依赖包

```bash
pip install -r requirements.txt
```

### 3. 数据库配置

#### 本地开发环境

1. 确保 PostgreSQL 服务已启动
2. 创建数据库

```bash
# 使用psql命令行工具
psql -U postgres
CREATE DATABASE trash-track;
\q

# 或者使用提供的初始化脚本
psql -U postgres -f init.sql
```

#### 使用 Docker

```bash
# 使用docker-compose启动数据库
docker-compose up -d postgres
```

### 4. 初始化数据库表

数据库表会在 Docker 容器启动时自动通过 init.sql 脚本创建。如果需要手动初始化：

```bash
# 使用psql执行初始化脚本
psql -U postgres -d trash-track -f init.sql
```

## 配置说明

### 环境变量

创建`.env`文件在项目根目录，包含以下配置：

```
# 数据库配置
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/trash-track

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 跨域配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 日志级别
LOG_LEVEL=INFO
```

### 数据库连接配置

数据库连接字符串格式：

```
postgresql://<用户名>:<密码>@<主机>:<端口>/<数据库名>
```

## 启动服务

### 开发环境

```bash
# 使用uvicorn启动开发服务器
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 或使用提供的启动脚本
python start.py
```

### 生产环境

#### 使用 Gunicorn (推荐)

```bash
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
```

#### 使用 Docker

```bash
# 构建并启动容器
docker-compose up -d backend
```

## 测试说明

### 运行单元测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest test_api.py

# 运行带有详细输出的测试
pytest -v
```

### 运行集成测试

```bash
# 运行完整系统测试
python ../test_complete_system.py
```

## 主要 API 端点

### 认证相关

-   `POST /api/v1/auth/login` - 用户登录
-   `POST /api/v1/auth/logout` - 用户登出
-   `GET /api/v1/auth/me` - 获取当前用户信息

### 投资组合管理

-   `GET /api/v1/portfolios/` - 获取所有投资组合
-   `POST /api/v1/portfolios/` - 创建新投资组合
-   `GET /api/v1/portfolios/{portfolio_id}` - 获取特定投资组合详情
-   `PUT /api/v1/portfolios/{portfolio_id}` - 更新投资组合
-   `DELETE /api/v1/portfolios/{portfolio_id}` - 删除投资组合

### 交易记录

-   `GET /api/v1/portfolios/{portfolio_id}/transactions` - 获取交易记录
-   `POST /api/v1/portfolios/{portfolio_id}/transactions` - 添加交易记录

### 持仓分析

-   `GET /api/v1/portfolios/{portfolio_id}/positions` - 获取持仓信息
-   `GET /api/v1/portfolios/{portfolio_id}/statistics` - 获取投资组合统计数据

## 项目结构

```
trash-track-api/
├── app/                    # 应用主目录
│   ├── api/                # API路由
│   │   ├── v1/             # API v1版本
│   │   │   ├── auth.py     # 认证相关API
│   │   │   ├── portfolios.py # 投资组合API
│   │   │   └── ...
│   ├── core/               # 核心功能
│   │   ├── config.py       # 配置管理
│   │   ├── security.py     # 安全相关
│   │   └── ...
│   ├── db/                 # 数据库相关
│   │   ├── base.py         # 基础模型
│   │   ├── session.py      # 数据库会话
│   │   └── ...
│   ├── models/             # 数据模型
│   ├── schemas/            # Pydantic模式
│   ├── services/           # 业务逻辑
│   └── main.py             # 应用入口
├── tests/                  # 测试目录
├── .env                    # 环境变量
├── init.sql                # 数据库初始化脚本
├── requirements.txt        # 依赖列表
└── start.py                # 启动脚本
```

## 常见问题

### 数据库连接错误

-   检查 PostgreSQL 服务是否运行
-   验证数据库连接字符串是否正确
-   确认数据库用户有足够权限

### 认证问题

-   检查 JWT 密钥配置
-   验证 token 过期时间设置
-   确认用户凭据正确

### 性能优化

-   使用 Redis 缓存频繁访问的数据
-   优化数据库查询
-   考虑使用异步处理长时间运行的任务
