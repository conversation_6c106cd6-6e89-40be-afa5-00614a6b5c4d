# 数据库表结构设计

## 概述

本文档描述了投资组合管理系统的数据库表结构设计，基于雪球数据格式进行设计，支持多投资组合、持仓管理、交易记录和银证转账功能。

## 表结构

### 1. 用户表 (users)

存储系统用户信息。

| 字段名        | 类型         | 约束                       | 说明         |
| ------------- | ------------ | -------------------------- | ------------ |
| id            | BIGINT       | PRIMARY KEY AUTO_INCREMENT | 用户 ID      |
| username      | VARCHAR(50)  | UNIQUE NOT NULL            | 用户名       |
| email         | VARCHAR(100) | UNIQUE                     | 邮箱         |
| password_hash | VARCHAR(255) | NOT NULL                   | 密码哈希     |
| is_active     | BOOLEAN      | DEFAULT TRUE               | 是否激活     |
| is_superuser  | BOOLEAN      | DEFAULT FALSE              | 是否超级用户 |
| created_at    | TIMESTAMP    | DEFAULT NOW()              | 创建时间     |
| updated_at    | TIMESTAMP    | DEFAULT NOW()              | 更新时间     |

### 2. 用户配置表 (user_settings)

存储用户个性化配置。

| 字段名        | 类型         | 约束          | 说明     |
| ------------- | ------------ | ------------- | -------- |
| id            | BIGINT         | PRIMARY KEY   | 配置 ID  |
| user_id       | BIGINT         | FOREIGN KEY   | 用户 ID  |
| setting_key   | VARCHAR(100) | NOT NULL      | 配置键   |
| setting_value | TEXT         |               | 配置值   |
| created_at    | TIMESTAMP    | DEFAULT NOW() | 创建时间 |
| updated_at    | TIMESTAMP    | DEFAULT NOW() | 更新时间 |

**约束**: UNIQUE(user_id, setting_key)

### 3. 投资组合表 (portfolios)

存储投资组合基本信息，对应雪球组合数据。

| 字段名           | 类型          | 约束          | 说明                     |
| ---------------- | ------------- | ------------- | ------------------------ |
| id               | BIGINT          | PRIMARY KEY   | 组合 ID                  |
| user_id          | BIGINT          | FOREIGN KEY   | 用户 ID                  |
| market           | VARCHAR(10)   | NOT NULL      | 市场代码 (CHA/US/HK/ALL) |
| name             | VARCHAR(100)  | NOT NULL      | 组合名称                 |
| assets           | DECIMAL(15,2) | DEFAULT 0     | 总资产                   |
| principal        | DECIMAL(15,2) | DEFAULT 0     | 本金                     |
| cash             | DECIMAL(15,2) | DEFAULT 0     | 现金                     |
| sign             | VARCHAR(5)    | DEFAULT '¥'   | 货币符号                 |
| currency         | VARCHAR(10)   | DEFAULT 'CNY' | 货币代码                 |
| market_value     | DECIMAL(15,2) | DEFAULT 0     | 市值                     |
| float_amount     | DECIMAL(15,2) | DEFAULT 0     | 浮动金额                 |
| float_rate       | DECIMAL(10,6) | DEFAULT 0     | 浮动比例                 |
| accum_amount     | DECIMAL(15,2) | DEFAULT 0     | 累计收益金额             |
| accum_rate       | DECIMAL(10,6) | DEFAULT 0     | 累计收益率               |
| day_float_amount | DECIMAL(15,2) | DEFAULT 0     | 日浮动金额               |
| day_float_rate   | DECIMAL(10,6) | DEFAULT 0     | 日浮动比例               |
| created_at       | TIMESTAMP     | DEFAULT NOW() | 创建时间                 |
| updated_at       | TIMESTAMP     | DEFAULT NOW() | 更新时间                 |

### 4. 品种持仓表 (holdings)

存储每个投资组合的持仓信息，对应雪球组合中的 list 数据。

| 字段名           | 类型          | 约束               | 说明                   |
| ---------------- | ------------- | ------------------ | ---------------------- |
| id               | BIGINT          | PRIMARY KEY        | 持仓 ID                |
| portfolio_id     | BIGINT          | FOREIGN KEY        | 投资组合 ID            |
| symbol           | VARCHAR(20)   | NOT NULL           | 证券代码 (如 SH513050) |
| name             | VARCHAR(100)  | NOT NULL           | 证券名称               |
| shares           | DECIMAL(15,2) | NOT NULL DEFAULT 0 | 持仓数量               |
| current          | DECIMAL(10,4) | DEFAULT 0          | 当前价格               |
| change           | DECIMAL(10,4) | DEFAULT 0          | 价格变动               |
| percentage       | DECIMAL(10,6) | DEFAULT 0          | 涨跌幅                 |
| currency         | VARCHAR(10)   | DEFAULT 'CNY'      | 货币                   |
| diluted_cost     | DECIMAL(10,4) | DEFAULT 0          | 摊薄成本               |
| hold_cost        | DECIMAL(10,4) | DEFAULT 0          | 持仓成本               |
| market_value     | DECIMAL(15,2) | DEFAULT 0          | 市值                   |
| float_amount     | DECIMAL(15,2) | DEFAULT 0          | 浮动金额               |
| float_rate       | DECIMAL(10,6) | DEFAULT 0          | 浮动比例               |
| accum_amount     | DECIMAL(15,2) | DEFAULT 0          | 累计收益金额           |
| accum_rate       | DECIMAL(10,6) | DEFAULT 0          | 累计收益率             |
| day_float_amount | DECIMAL(15,2) | DEFAULT 0          | 日浮动金额             |
| day_float_rate   | DECIMAL(10,6) | DEFAULT 0          | 日浮动比例             |
| created_at       | TIMESTAMP     | DEFAULT NOW()      | 创建时间               |
| updated_at       | TIMESTAMP     | DEFAULT NOW()      | 更新时间               |

**约束**: UNIQUE(portfolio_id, symbol)

### 5. 品种交易记录表 (transactions)

存储交易记录，对应雪球交易记录数据。

| 字段名               | 类型          | 约束          | 说明                                                     |
| -------------------- | ------------- | ------------- | -------------------------------------------------------- |
| id                   | BIGINT          | PRIMARY KEY   | 交易 ID                                                  |
| portfolio_id         | BIGINT          | FOREIGN KEY   | 投资组合 ID                                              |
| symbol               | VARCHAR(20)   | NOT NULL      | 证券代码                                                 |
| name                 | VARCHAR(100)  | NOT NULL      | 证券名称                                                 |
| type                 | INTEGER       | NOT NULL      | 交易类型 (1=买入,2=卖出,3=分红,4=拆股,5=合股,6=除权除息) |
| time                 | BIGINT        | NOT NULL      | 交易时间戳(毫秒)                                         |
| shares               | DECIMAL(15,2) | NOT NULL      | 交易数量                                                 |
| price                | DECIMAL(10,4) | NOT NULL      | 交易价格                                                 |
| comment              | TEXT          |               | 备注                                                     |
| commission           | DECIMAL(10,2) |               | 佣金                                                     |
| tax                  | DECIMAL(10,2) |               | 税费                                                     |
| commission_rate      | DECIMAL(10,6) |               | 佣金率                                                   |
| tax_rate             | DECIMAL(10,6) |               | 税率                                                     |
| unit_shares          | DECIMAL(15,2) |               | 单位股数(拆股合股用)                                     |
| unit_dividend        | DECIMAL(10,4) |               | 单位分红                                                 |
| unit_increase_shares | DECIMAL(15,2) |               | 单位增股                                                 |
| record_date          | BIGINT        |               | 记录日期时间戳                                           |
| type_name            | VARCHAR(20)   |               | 交易类型名称                                             |
| desc                 | TEXT          |               | 描述                                                     |
| amount               | DECIMAL(15,2) | NOT NULL      | 交易金额                                                 |
| created_at           | TIMESTAMP     | DEFAULT NOW() | 创建时间                                                 |
| updated_at           | TIMESTAMP     | DEFAULT NOW() | 更新时间                                                 |

### 6. 银证转账表 (bank_transfers)

存储银证转账记录，对应雪球转账记录数据。

| 字段名       | 类型          | 约束          | 说明                     |
| ------------ | ------------- | ------------- | ------------------------ |
| id           | BIGINT          | PRIMARY KEY   | 转账 ID                  |
| portfolio_id | BIGINT          | FOREIGN KEY   | 投资组合 ID              |
| type         | INTEGER       | NOT NULL      | 转账类型 (1=转入,2=转出) |
| market       | VARCHAR(10)   | NOT NULL      | 市场代码                 |
| amount       | DECIMAL(15,2) | NOT NULL      | 转账金额                 |
| time         | BIGINT        | NOT NULL      | 转账时间戳(毫秒)         |
| created_at   | TIMESTAMP     | DEFAULT NOW() | 创建时间                 |
| updated_at   | TIMESTAMP     | DEFAULT NOW() | 更新时间                 |

## 表关系

```
users (1) -----> (N) portfolios
portfolios (1) -----> (N) holdings
portfolios (1) -----> (N) transactions
portfolios (1) -----> (N) bank_transfers
users (1) -----> (N) user_settings
```

## 索引设计

### 主要索引

-   `idx_users_username` - users(username)
-   `idx_users_email` - users(email)
-   `idx_portfolios_user_id` - portfolios(user_id)
-   `idx_portfolios_market` - portfolios(market)
-   `idx_holdings_portfolio_id` - holdings(portfolio_id)
-   `idx_holdings_symbol` - holdings(symbol)
-   `idx_transactions_portfolio_id` - transactions(portfolio_id)
-   `idx_transactions_symbol` - transactions(symbol)
-   `idx_transactions_time` - transactions(time)
-   `idx_transactions_type` - transactions(type)
-   `idx_bank_transfers_portfolio_id` - bank_transfers(portfolio_id)
-   `idx_bank_transfers_type` - bank_transfers(type)
-   `idx_bank_transfers_time` - bank_transfers(time)

## 触发器

所有表都配置了自动更新 `updated_at` 字段的触发器：

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';
```

## 数据类型说明

-   **BIGINT**: 使用 PostgreSQL 的 BIGINT 类型作为主键
-   **DECIMAL**: 用于存储精确的金额和比例数据
-   **BIGINT**: 用于存储时间戳(毫秒)
-   **TIMESTAMP WITH TIME ZONE**: 用于存储创建和更新时间

## 约束说明

-   所有金额字段使用 DECIMAL 类型确保精度
-   时间戳使用 BIGINT 存储毫秒级时间戳，与雪球数据格式保持一致
-   证券代码和投资组合 ID 的组合唯一，避免重复持仓
-   用户配置的键值对唯一，避免重复配置
