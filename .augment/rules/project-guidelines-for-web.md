---
type: "agent_requested"
description: "This is a development guide for the `trash-track-web` project."
---

# Development Guide for `trash-track-web`

This document outlines the rules and conventions for AI-assisted development on the `trash-track-web` project. Adhering to these guidelines will ensure consistency, maintainability, and alignment with the project's existing architecture and style.

## 1. Core Technologies & Frameworks

-   **Framework**: [Next.js](https://nextjs.org/) (v15) with the App Router.
-   **Language**: [TypeScript](https://www.typescriptlang.org/).
-   **UI Components**: [shadcn/ui](https://ui.shadcn.com/).
-   **Styling**: [Tailwind CSS](https://tailwindcss.com/) with a custom theme system.
-   **State Management**: [Zustand](https://github.com/pmndrs/zustand).
-   **Forms**: [React Hook Form](https://react-hook-form.com/) with [Zod](https://zod.dev/) for validation.
-   **Charts**: [Recharts](https://recharts.org/).
-   **Icons**: [Lucide React](https://lucide.dev/guide/packages/lucide-react).

## 2. Code Style & Formatting

-   **Formatting**: Code is formatted with [Prettier](https://prettier.io/).
    -   **Key Rules**:
        -   `printWidth`: 120 characters
        -   `tabWidth`: 2 spaces
        -   `singleQuote`: `false` (use double quotes)
        -   `trailingComma`: `all`
        -   `semi`: `true`
-   **Linting**: [ESLint](https://eslint.org/) is used for code quality and consistency.
    -   **Key Rules**:
        -   **File Naming**: `kebab-case` (e.g., `my-component.tsx`).
        -   **Import Order**: Enforced via `eslint-plugin-import`. External libraries first, then internal modules.
        -   **Complexity**: Keep functions and components simple (`complexity` < 10, `max-depth` < 4, `max-lines` < 300).
        -   **React**:
            -   Component names must be `PascalCase`.
            -   Avoid unstable nested components.
            -   Do not use array indexes as keys.

## 3. Project Structure & Conventions

-   **Directory Structure**:
    -   `src/app`: Contains the Next.js App Router pages and layouts.
        -   `(main)`: Main application routes (e.g., dashboard).
        -   `(external)`: Routes outside the main authenticated layout.
    -   `src/components`: Reusable components.
        -   `ui`: Core `shadcn/ui` components.
        -   `data-table`: Components related to the data table implementation.
    -   `src/lib`: Utility functions (`utils.ts`).
    -   `src/hooks`: Custom React hooks.
    -   `src/stores`: Zustand state management stores.
    -   `src/types`: TypeScript type definitions.
-   **Component Design**:
    -   **Server Components**: Use `"use server"` for server-side logic (Server Actions).
    -   **Client Components**: Use `"use client"` at the top of files requiring client-side interactivity (hooks, event handlers).
    -   **Props**: Define component props using TypeScript interfaces or types.
    -   **Styling**: Use `clsx` and `tailwind-merge` for conditional and combined class names.

## 4. Theming & Styling

-   **CSS Variables**: The theme is built on CSS variables defined in `src/app/globals.css`.
-   **Theme Presets**: The project supports multiple theme presets (`brutalist`, `soft-pop`, `tangerine`) located in `src/styles/presets`.
-   **Dark Mode**: Implemented using the `.dark` class and corresponding CSS variables.
-   **Customization**: To add or modify styles, edit the CSS variables in `src/app/globals.css` or create a new preset file.

## 5. State Management

-   **Zustand**: Used for global state management.
-   **Stores**:
    -   Defined in the `src/stores` directory.
    -   Follow the pattern of creating a vanilla store with `createStore` and providing a React provider.
    -   Example: `src/stores/preferences/preferences-store.ts`.

## 6. Server-Side Logic

-   **Server Actions**: Use Server Actions for mutations and data fetching from the client.
    -   Place Server Actions in `src/server/server-actions.ts`.
    -   Use `"use server"` at the top of the file.
-   **Cookies**: Interact with cookies using the `next/headers` `cookies()` function within Server Actions.

## 7. Best Practices

### TypeScript

-   **Strict Typing**: Always use specific types. Avoid `any` unless absolutely necessary.
-   **Generics**: Use generics to create reusable and type-safe components and functions.
-   **Type vs. Interface**: Prefer `type` for defining props and simple object shapes. Use `interface` for objects that might be extended in the future.
-   **Readonly**: Use the `readonly` modifier for props and state that should not be mutated.

### Next.js

-   **Performance**:
    -   **Server Components**: Default to Server Components for better performance. Only use Client Components (`"use client"`) when client-side interactivity is required.
    -   **Dynamic Imports**: Use `next/dynamic` to lazy-load components that are not visible on the initial page load.
    -   **Image Optimization**: Use the `next/image` component for all images to ensure they are optimized for performance.
-   **Security**:
    -   **Server Actions**: Perform all data mutations through Server Actions to prevent exposing sensitive logic to the client.
    -   **Input Validation**: Always validate input on the server using Zod, even if it's already validated on the client.
    -   **Environment Variables**: Store all secrets and keys in environment variables (`.env.local`) and access them using `process.env`.

### shadcn/ui & Tailwind CSS

-   **Component Composition**: Build complex components by composing simpler ones from `shadcn/ui`.
-   **Styling**:
    -   Use `tailwind-merge` to handle conflicting style classes.
    -   Use `cva` (class-variance-authority) to create variants for your components.
-   **Accessibility (a11y)**:
    -   Use semantic HTML elements (e.g., `<button>`, `<nav>`).
    -   Ensure all interactive elements are focusable and have clear focus states.
    -   Add `aria-*` attributes where necessary to improve screen reader support.

### General

-   **Error Handling**:
    -   Use `try...catch` blocks in Server Actions and data fetching functions.
    -   Implement error boundaries in React to gracefully handle rendering errors.
-   **Code Comments**:
    -   Write comments to explain the "why" behind complex or non-obvious code, not the "what".

## 8. Additional Rules

-   **Eslint Cleanliness**: All code must pass the eslint rules checks before compile. No errors.
