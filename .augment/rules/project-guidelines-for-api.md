---
type: "agent_requested"
description: "This is a development guide for the `trash-track-api` project."
---

# Project Guidelines for `trash-track-api`

This document outlines the rules and conventions for AI-assisted development on the `trash-track-api` project. Adhering to these guidelines will ensure consistency, maintainability, and alignment with the project's existing architecture and style.

## 1. Core Technologies & Frameworks

-   **Framework**: [FastAPI](https://fastapi.tiangolo.com/)
-   **Language**: [Python](https://www.python.org/) (3.9+)
-   **ORM**: [SQLAlchemy](https://www.sqlalchemy.org/) for database interaction.
-   **Data Validation**: [Pydantic](https://docs.pydantic.dev/latest/) for data validation and serialization.
-   **Database Migrations**: [Alembic](https://alembic.sqlalchemy.org/en/latest/) for managing database schema changes.
-   **Testing**: [Pytest](https://docs.pytest.org/) for unit and integration testing.
-   **Linting**: [Ruff](https://github.com/astral-sh/ruff) for fast, comprehensive code quality checks.
-   **Formatting**: [Black](https://github.com/psf/black) for uncompromising code formatting.

## 2. Code Style & Formatting

-   **Formatting**: Code is formatted with **Black**.
    -   **Key Rules**: Default Black configuration (e.g., `line-length` = 88, double quotes).
-   **Linting**: **Ruff** is used for code quality, import sorting, and consistency.
    -   **Key Rules**:
        -   **File Naming**: `snake_case` (e.g., `portfolio_service.py`).
        -   **Variable/Function Naming**: `snake_case` (e.g., `get_user_portfolios`).
        -   **Class Naming**: `PascalCase` (e.g., `PortfolioService`).
        -   **Import Order**: Handled automatically by Ruff.
        -   **Complexity**: Keep functions and methods simple.

## 3. Project Structure & Conventions

-   **Directory Structure**:
    -   `src/app/api`: Contains FastAPI routers and API endpoints.
        -   **Responsibility**: Handles HTTP request/response cycle, calls service layer functions. **No business logic here.**
    -   `src/app/services`: Contains business logic.
        -   **Responsibility**: Implements core application logic, interacts with the database via models.
    -   `src/app/models`: Contains SQLAlchemy ORM models.
        -   **Responsibility**: Defines the database table structure and relationships.
    -   `src/app/schemas`: Contains Pydantic schemas.
        -   **Responsibility**: Defines data shapes for API input and output, ensuring type safety and validation.
    -   `src/app/core`: Contains core configuration and utilities (e.g., database connection, settings).
    -   `src/migrations`: Contains Alembic database migration scripts.
    -   `src/tests`: Contains all Pytest tests.

## 4. Best Practices

### Python & Type Hinting

-   **Strict Typing**: Always use specific type hints from the `typing` module. Avoid `Any` whenever possible.
-   **Readonly/Constants**: Use `typing.Final` to denote constants that should not be changed.
-   **Clarity**: Write clear, Pythonic code. Follow the Zen of Python (`import this`).

### FastAPI

-   **Performance**:
    -   **Async Operations**: Use `async def` for all endpoints that perform I/O operations (e.g., database calls, external API requests).
    -   **Background Tasks**: For long-running operations that don't need to block the response, use `BackgroundTasks`.
-   **Security**:
    -   **Dependency Injection**: Use the `Depends` system for authentication (`get_current_user`) and database sessions (`get_db`).
    -   **Input Validation**: Rely on Pydantic schemas for automatic request validation.
    -   **Environment Variables**: Store all secrets and keys in environment variables (`.env`), never hardcode them. Access them via a settings management class in `app/core/config.py`.

### SQLAlchemy & Pydantic

-   **Schema Separation**: Strictly separate schemas for different operations to control data exposure and prevent vulnerabilities.
    -   `PortfolioCreate`: For creating a new portfolio (input).
    -   `PortfolioUpdate`: For updating an existing portfolio (input).
    -   `PortfolioResponse`: For returning portfolio data to the client (output).
-   **Efficient Queries**:
    -   Avoid the N+1 problem by using `selectinload` or `joinedload` for related models in your queries.
    -   Filter data in the database, not in your Python code, to minimize memory usage.
-   **Data Integrity**: Use database-level constraints (e.g., `ForeignKey`, `nullable=False`) in your SQLAlchemy models to ensure data integrity.

### General

-   **Error Handling**:
    -   Use `try...except` blocks within the **service layer** to handle specific, expected errors (e.g., `sqlalchemy.exc.IntegrityError`).
    -   Raise `fastapi.HTTPException` from the **API layer** to send appropriate HTTP status codes and error details to the client.
-   **Code Comments**: Write comments to explain the "why" behind complex or non-obvious code, not the "what".
-   **Testing**:
    -   All new features and bug fixes **must** be accompanied by tests.
    -   Use a separate database for testing to ensure isolation.

## 5. Additional Rules

-   **Linter & Formatter Cleanliness**: All code must pass `ruff` and `black` checks before compile or reload. No exceptions.
