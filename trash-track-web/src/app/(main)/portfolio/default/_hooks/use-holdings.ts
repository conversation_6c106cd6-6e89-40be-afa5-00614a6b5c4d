"use client";

import { useState, useCallback, useEffect } from "react";

import { toast } from "sonner";

import {
  Holding,
  Transaction,
  BankTransfer,
  BuyTransactionData,
  SellTransactionData,
  TransferData,
} from "@/types/portfolio";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:8000";

function getAuthHeaders() {
  const token = localStorage.getItem("auth_token");
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
}

export function useHoldings(portfolioId?: number) {
  const [holdings, setHoldings] = useState<Holding[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [transfers, setTransfers] = useState<BankTransfer[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchHoldings = useCallback(async (id: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/holdings`, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setHoldings(data);
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to fetch holdings");
      }
    } catch (error) {
      console.error("Failed to fetch holdings:", error);
      toast.error("Failed to fetch holdings");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchTransactions = useCallback(async (id: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/transactions`, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setTransactions(data);
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to fetch transactions");
      }
    } catch (error) {
      console.error("Failed to fetch transactions:", error);
      toast.error("Failed to fetch transactions");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchTransfers = useCallback(async (id: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/transfers`, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setTransfers(data);
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to fetch transfers");
      }
    } catch (error) {
      console.error("Failed to fetch transfers:", error);
      toast.error("Failed to fetch transfers");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const buyStock = useCallback(
    async (id: number, data: BuyTransactionData) => {
      try {
        setIsLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/buy`, {
          method: "POST",
          headers: getAuthHeaders(),
          body: JSON.stringify(data),
        });

        if (response.ok) {
          const transaction = await response.json();
          setTransactions((prev) => [transaction, ...prev]);
          // Refresh holdings to get updated data
          await fetchHoldings(id);
          toast.success("Stock purchased successfully");
          return transaction;
        } else {
          const error = await response.json();
          toast.error(error.detail ?? "Failed to buy stock");
          throw new Error(error.detail ?? "Failed to buy stock");
        }
      } catch (error) {
        console.error("Failed to buy stock:", error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [fetchHoldings],
  );

  const sellStock = useCallback(
    async (id: number, data: SellTransactionData) => {
      try {
        setIsLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/sell`, {
          method: "POST",
          headers: getAuthHeaders(),
          body: JSON.stringify(data),
        });

        if (response.ok) {
          const transaction = await response.json();
          setTransactions((prev) => [transaction, ...prev]);
          // Refresh holdings to get updated data
          await fetchHoldings(id);
          toast.success("Stock sold successfully");
          return transaction;
        } else {
          const error = await response.json();
          toast.error(error.detail ?? "Failed to sell stock");
          throw new Error(error.detail ?? "Failed to sell stock");
        }
      } catch (error) {
        console.error("Failed to sell stock:", error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [fetchHoldings],
  );

  const bankTransfer = useCallback(async (id: number, data: TransferData) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/transfer`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const transfer = await response.json();
        setTransfers((prev) => [transfer, ...prev]);
        toast.success("Transfer completed successfully");
        return transfer;
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to complete transfer");
        throw new Error(error.detail ?? "Failed to complete transfer");
      }
    } catch (error) {
      console.error("Failed to complete transfer:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteHolding = useCallback(async (portfolioId: number, holdingId: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${portfolioId}/holdings/${holdingId}`, {
        method: "DELETE",
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        // Remove the holding from local state
        setHoldings((prev) => prev.filter((holding) => holding.id !== holdingId));
        toast.success("Holding deleted successfully");
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to delete holding");
        throw new Error(error.detail ?? "Failed to delete holding");
      }
    } catch (error) {
      console.error("Failed to delete holding:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateTransaction = useCallback(
    async (portfolioId: number, transactionId: number, data: Partial<Transaction>) => {
      try {
        setIsLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${portfolioId}/transactions/${transactionId}`, {
          method: "PUT",
          headers: getAuthHeaders(),
          body: JSON.stringify(data),
        });

        if (response.ok) {
          toast.success("交易记录更新成功");
          await fetchTransactions(portfolioId);
          await fetchHoldings(portfolioId);
        } else {
          const error = await response.json();
          toast.error(error.detail ?? "更新交易记录失败");
          throw new Error(error.detail ?? "更新交易记录失败");
        }
      } catch (error) {
        console.error("Failed to update transaction:", error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [fetchTransactions, fetchHoldings],
  );

  const deleteTransaction = useCallback(
    async (portfolioId: number, transactionId: number) => {
      try {
        setIsLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${portfolioId}/transactions/${transactionId}`, {
          method: "DELETE",
          headers: getAuthHeaders(),
        });

        if (response.ok) {
          toast.success("交易记录删除成功");
          await fetchTransactions(portfolioId);
          await fetchHoldings(portfolioId);
        } else {
          const error = await response.json();
          toast.error(error.detail ?? "删除交易记录失败");
          throw new Error(error.detail ?? "删除交易记录失败");
        }
      } catch (error) {
        console.error("Failed to delete transaction:", error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [fetchTransactions, fetchHoldings],
  );

  // Auto-fetch data when portfolioId changes
  useEffect(() => {
    if (portfolioId) {
      fetchHoldings(portfolioId);
      fetchTransactions(portfolioId);
      fetchTransfers(portfolioId);
    }
  }, [portfolioId, fetchHoldings, fetchTransactions, fetchTransfers]);

  return {
    holdings,
    transactions,
    transfers,
    isLoading,
    fetchHoldings,
    fetchTransactions,
    fetchTransfers,
    buyStock,
    sellStock,
    bankTransfer,
    deleteHolding,
    updateTransaction,
    deleteTransaction,
  };
}
