"use client";

import { useState, useCallback, useEffect } from "react";

import { toast } from "sonner";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:8000";

interface Holding {
  id: number;
  portfolio_id: number;
  symbol: string;
  name: string;
  shares: number;
  current: number;
  change: number;
  percentage: number;
  currency: string;
  diluted_cost: number;
  hold_cost: number;
  market_value: number;
  float_amount: number;
  float_rate: number;
  accum_amount: number;
  accum_rate: number;
  day_float_amount: number;
  day_float_rate: number;
  created_at: string;
  updated_at: string;
}

interface Transaction {
  id: number;
  portfolio_id: number;
  symbol: string;
  name: string;
  type: number; // 1=买入，2=卖出，3=分红，4=拆股，5=合股，6=除权除息
  time: number;
  shares: number;
  price: number;
  comment?: string;
  commission?: number;
  tax?: number;
  amount: number;
  created_at: string;
  updated_at: string;
}

interface BankTransfer {
  id: number;
  portfolio_id: number;
  type: number; // 1=转入，2=转出
  market: string;
  amount: number;
  time: number;
  created_at: string;
  updated_at: string;
}

interface BuyTransactionData {
  symbol: string;
  name: string;
  shares: number;
  price: number;
  comment?: string;
}

interface SellTransactionData {
  symbol: string;
  shares: number;
  price: number;
  comment?: string;
}

interface TransferData {
  type: number; // 1=转入，2=转出
  amount: number;
  comment?: string;
}

function getAuthHeaders() {
  const token = localStorage.getItem("auth_token");
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
}

// 安全地将字符串或数字转换为数字
export function toNumber(value: string | number): number {
  if (typeof value === "number") {
    return value;
  }
  const num = parseFloat(value);
  return isNaN(num) ? 0 : num;
}

export function useHoldings(portfolioId?: number) {
  const [holdings, setHoldings] = useState<Holding[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [transfers, setTransfers] = useState<BankTransfer[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchHoldings = useCallback(async (id: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/holdings`, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setHoldings(data);
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to fetch holdings");
      }
    } catch (error) {
      console.error("Failed to fetch holdings:", error);
      toast.error("Failed to fetch holdings");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchTransactions = useCallback(async (id: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/transactions`, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setTransactions(data);
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to fetch transactions");
      }
    } catch (error) {
      console.error("Failed to fetch transactions:", error);
      toast.error("Failed to fetch transactions");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchTransfers = useCallback(async (id: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/transfers`, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setTransfers(data);
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to fetch transfers");
      }
    } catch (error) {
      console.error("Failed to fetch transfers:", error);
      toast.error("Failed to fetch transfers");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const buyStock = useCallback(
    async (id: number, data: BuyTransactionData) => {
      try {
        setIsLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/buy`, {
          method: "POST",
          headers: getAuthHeaders(),
          body: JSON.stringify(data),
        });

        if (response.ok) {
          const transaction = await response.json();
          setTransactions((prev) => [transaction, ...prev]);
          // Refresh holdings to get updated data
          await fetchHoldings(id);
          toast.success("Stock purchased successfully");
          return transaction;
        } else {
          const error = await response.json();
          toast.error(error.detail ?? "Failed to buy stock");
          throw new Error(error.detail ?? "Failed to buy stock");
        }
      } catch (error) {
        console.error("Failed to buy stock:", error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [fetchHoldings],
  );

  const sellStock = useCallback(
    async (id: number, data: SellTransactionData) => {
      try {
        setIsLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/sell`, {
          method: "POST",
          headers: getAuthHeaders(),
          body: JSON.stringify(data),
        });

        if (response.ok) {
          const transaction = await response.json();
          setTransactions((prev) => [transaction, ...prev]);
          // Refresh holdings to get updated data
          await fetchHoldings(id);
          toast.success("Stock sold successfully");
          return transaction;
        } else {
          const error = await response.json();
          toast.error(error.detail ?? "Failed to sell stock");
          throw new Error(error.detail ?? "Failed to sell stock");
        }
      } catch (error) {
        console.error("Failed to sell stock:", error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [fetchHoldings],
  );

  const bankTransfer = useCallback(async (id: number, data: TransferData) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}/transfer`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const transfer = await response.json();
        setTransfers((prev) => [transfer, ...prev]);
        toast.success("Transfer completed successfully");
        return transfer;
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to complete transfer");
        throw new Error(error.detail ?? "Failed to complete transfer");
      }
    } catch (error) {
      console.error("Failed to complete transfer:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteHolding = useCallback(async (portfolioId: number, holdingId: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${portfolioId}/holdings/${holdingId}`, {
        method: "DELETE",
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        // Remove the holding from local state
        setHoldings((prev) => prev.filter((holding) => holding.id !== holdingId));
        toast.success("Holding deleted successfully");
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to delete holding");
        throw new Error(error.detail ?? "Failed to delete holding");
      }
    } catch (error) {
      console.error("Failed to delete holding:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Auto-fetch data when portfolioId changes
  useEffect(() => {
    if (portfolioId) {
      fetchHoldings(portfolioId);
      fetchTransactions(portfolioId);
      fetchTransfers(portfolioId);
    }
  }, [portfolioId, fetchHoldings, fetchTransactions, fetchTransfers]);

  return {
    holdings,
    transactions,
    transfers,
    isLoading,
    fetchHoldings,
    fetchTransactions,
    fetchTransfers,
    buyStock,
    sellStock,
    bankTransfer,
    deleteHolding,
  };
}
