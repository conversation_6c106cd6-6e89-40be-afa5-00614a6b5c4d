"use client";

import { useState, useCallback } from "react";

import { toast } from "sonner";

interface Portfolio {
  id: number;
  user_id: number;
  market: string;
  name: string;
  assets: number;
  principal: number;
  cash: number;
  sign: string;
  currency: string;
  market_value: number;
  float_amount: number;
  float_rate: number;
  accum_amount: number;
  accum_rate: number;
  day_float_amount: number;
  day_float_rate: number;
  created_at: string;
  updated_at: string;
}

interface CreatePortfolioData {
  market: string;
  name: string;
  assets?: number;
  principal?: number;
  cash?: number;
  sign?: string;
  currency?: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:8000";

export function usePortfolios() {
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const getAuthHeaders = () => {
    const token = localStorage.getItem("auth_token");
    return {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  };

  const refreshPortfolios = useCallback(async () => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/`, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setPortfolios(data);
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to fetch portfolios");
      }
    } catch (error) {
      console.error("Failed to fetch portfolios:", error);
      toast.error("Failed to fetch portfolios");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createPortfolio = useCallback(async (data: CreatePortfolioData) => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const newPortfolio = await response.json();
        setPortfolios((prev) => [...prev, newPortfolio]);
        toast.success("Portfolio created successfully");
        return newPortfolio;
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to create portfolio");
        throw new Error(error.detail ?? "Failed to create portfolio");
      }
    } catch (error) {
      console.error("Failed to create portfolio:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getPortfolio = useCallback(async (id: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}`, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const portfolio = await response.json();
        return portfolio;
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to fetch portfolio");
        throw new Error(error.detail ?? "Failed to fetch portfolio");
      }
    } catch (error) {
      console.error("Failed to fetch portfolio:", error);
      throw error;
    }
  }, []);

  const updatePortfolio = useCallback(async (id: number, data: Partial<CreatePortfolioData>) => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}`, {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const updatedPortfolio = await response.json();
        setPortfolios((prev) => prev.map((p) => (p.id === id ? updatedPortfolio : p)));
        toast.success("Portfolio updated successfully");
        return updatedPortfolio;
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to update portfolio");
        throw new Error(error.detail ?? "Failed to update portfolio");
      }
    } catch (error) {
      console.error("Failed to update portfolio:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deletePortfolio = useCallback(async (id: number) => {
    try {
      setIsLoading(true);

      const response = await fetch(`${API_BASE_URL}/api/v1/portfolios/${id}`, {
        method: "DELETE",
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        setPortfolios((prev) => prev.filter((p) => p.id !== id));
        toast.success("Portfolio deleted successfully");
      } else {
        const error = await response.json();
        toast.error(error.detail ?? "Failed to delete portfolio");
        throw new Error(error.detail ?? "Failed to delete portfolio");
      }
    } catch (error) {
      console.error("Failed to delete portfolio:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    portfolios,
    isLoading,
    refreshPortfolios,
    createPortfolio,
    getPortfolio,
    updatePortfolio,
    deletePortfolio,
  };
}
