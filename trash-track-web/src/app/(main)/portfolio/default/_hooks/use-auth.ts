"use client";

import { useState, useEffect, useCallback } from "react";

import { toast } from "sonner";

interface User {
  id: string;
  username: string;
  email?: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isInitializing: boolean;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL ?? "http://localhost:8000";

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: false,
    isInitializing: true,
  });

  const getCurrentUser = useCallback(async (token: string) => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true }));

      const response = await fetch(`${API_BASE_URL}/api/v1/auth/me`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const user = await response.json();
        setAuthState((prev) => ({ ...prev, user, token, isLoading: false, isInitializing: false }));
      } else {
        // Token is invalid, clear it
        localStorage.removeItem("auth_token");
        setAuthState({ user: null, token: null, isLoading: false, isInitializing: false });
      }
    } catch (error) {
      console.error("Failed to get current user:", error);
      localStorage.removeItem("auth_token");
      setAuthState({ user: null, token: null, isLoading: false, isInitializing: false });
    }
  }, []);

  // Load token from localStorage on mount
  useEffect(() => {
    const token = localStorage.getItem("auth_token");
    if (token) {
      setAuthState((prev) => ({ ...prev, token }));
      // Verify token and get user info
      getCurrentUser(token);
    } else {
      // No token found, finish initialization
      setAuthState((prev) => ({ ...prev, isInitializing: false }));
    }
  }, [getCurrentUser]);

  const login = useCallback(
    async (username: string, password: string) => {
      try {
        setAuthState((prev) => ({ ...prev, isLoading: true }));

        const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ username, password }),
        });

        if (response.ok) {
          const { access_token } = await response.json();
          localStorage.setItem("auth_token", access_token);

          // Get user info
          await getCurrentUser(access_token);

          toast.success("Login successful");
        } else {
          const error = await response.json();
          toast.error(error.detail ?? "Login failed");
          setAuthState((prev) => ({ ...prev, isLoading: false }));
        }
      } catch (error) {
        console.error("Login error:", error);
        toast.error("Login failed");
        setAuthState((prev) => ({ ...prev, isLoading: false }));
      }
    },
    [getCurrentUser],
  );

  const logout = useCallback(async () => {
    try {
      if (authState.token) {
        await fetch(`${API_BASE_URL}/api/v1/auth/logout`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${authState.token}`,
          },
        });
      }
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      localStorage.removeItem("auth_token");
      setAuthState({ user: null, token: null, isLoading: false, isInitializing: false });
      toast.success("Logged out successfully");
    }
  }, [authState.token]);

  return {
    user: authState.user,
    token: authState.token,
    isLoading: authState.isLoading,
    isInitializing: authState.isInitializing,
    login,
    logout,
  };
}
