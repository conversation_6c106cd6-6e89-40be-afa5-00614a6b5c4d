"use client";

import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface CreatePortfolioFormProps {
  onSubmit: (data: {
    market: string;
    name: string;
    assets?: number;
    principal?: number;
    cash?: number;
    sign?: string;
    currency?: string;
  }) => Promise<void>;
  onCancel?: () => void;
}

export function CreatePortfolioForm({ onSubmit, onCancel }: CreatePortfolioFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    market: "CHA",
    principal: 0,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = "投资组合名称不能为空";
    } else if (formData.name.trim().length > 50) {
      newErrors.name = "投资组合名称不能超过50个字符";
    }

    if (formData.principal < 0) {
      newErrors.principal = "初始资金不能为负数";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      await onSubmit({
        market: formData.market,
        name: formData.name.trim(),
        principal: formData.principal,
        cash: formData.principal,
        assets: formData.principal,
        sign: "¥",
        currency: "CNY",
      });

      // Reset form
      setFormData({
        name: "",
        market: "CHA",
        principal: 0,
      });
      setErrors({});
    } catch (error) {
      console.error("Failed to create portfolio:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">投资组合名称 *</Label>
        <Input
          id="name"
          type="text"
          value={formData.name}
          onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
          placeholder="请输入投资组合名称"
          required
          maxLength={50}
        />
        {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="market">市场</Label>
        <Select value={formData.market} onValueChange={(value) => setFormData((prev) => ({ ...prev, market: value }))}>
          <SelectTrigger>
            <SelectValue placeholder="选择市场" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="CHA">A股市场</SelectItem>
            <SelectItem value="US">美股市场</SelectItem>
            <SelectItem value="HK">港股市场</SelectItem>
            <SelectItem value="ALL">全球市场</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="principal">初始资金 (¥)</Label>
        <Input
          id="principal"
          type="number"
          min="0"
          step="0.01"
          value={formData.principal}
          onChange={(e) => setFormData((prev) => ({ ...prev, principal: parseFloat(e.target.value) || 0 }))}
          placeholder="请输入初始资金金额"
        />
        {errors.principal && <p className="text-sm text-red-500">{errors.principal}</p>}
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            取消
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting || !formData.name.trim()}>
          {isSubmitting ? "创建中..." : "创建投资组合"}
        </Button>
      </div>
    </form>
  );
}
