"use client";

import * as React from "react";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { BankTransfer, formatCurrency, formatDateTime, getTransferTypeName } from "@/types/portfolio";

interface TransfersTableProps {
  transfers: Transfer[];
  isLoading: boolean;
}

export function TransfersTable({ transfers, isLoading }: TransfersTableProps) {
  const formatCurrency = (amount: number, sign: string = "¥") => {
    return `${sign}${amount.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const getTransferTypeName = (type: number) => {
    return type === 1 ? "转入" : "转出";
  };

  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString("zh-CN");
  };

  if (isLoading) {
    return (
      <div className="p-8 text-center">
        <p className="text-muted-foreground">加载中...</p>
      </div>
    );
  }

  if (transfers.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-muted-foreground">暂无转账记录</p>
      </div>
    );
  }

  return (
    <div className="bg-background/95 supports-[backdrop-filter]:bg-background/60 rounded-lg border backdrop-blur">
      <Table>
        <TableHeader>
          <TableRow className="hover:bg-muted/50">
            <TableHead className="font-medium">转账时间</TableHead>
            <TableHead className="font-medium">类型</TableHead>
            <TableHead className="text-right font-medium">金额</TableHead>
            <TableHead className="font-medium">备注</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transfers.map((transfer) => (
            <TableRow key={transfer.id} className="hover:bg-muted/50 transition-colors">
              <TableCell className="font-mono text-sm">{formatDateTime(transfer.created_at)}</TableCell>
              <TableCell>
                <span className={transfer.type === 1 ? "text-green-600" : "text-red-600"}>
                  {getTransferTypeName(transfer.type)}
                </span>
              </TableCell>
              <TableCell className="text-right font-mono">
                <span className={transfer.type === 1 ? "text-green-600" : "text-red-600"}>
                  {transfer.type === 1 ? "+" : "-"}
                  {formatCurrency(transfer.amount)}
                </span>
              </TableCell>
              <TableCell className="text-muted-foreground text-sm">{transfer.comment ?? "-"}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
