"use client";

import * as React from "react";
import { Edit, Trash2, MoreH<PERSON>zontal } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import {
  Transaction,
  formatCurrency,
  formatDate,
  getTransactionTypeName,
  getTransactionTypeColor,
} from "@/types/portfolio";

interface TransactionHistoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSymbol: string;
  transactions: Transaction[];
  onEditTransaction?: (transaction: Transaction) => void;
  onDeleteTransaction?: (transactionId: number) => void;
}

export function TransactionHistoryDialog({
  isOpen,
  onClose,
  selectedSymbol,
  transactions,
  onEditTransaction,
  onDeleteTransaction,
}: TransactionHistoryDialogProps) {
  const filteredTransactions = transactions.filter((transaction) => transaction.symbol === selectedSymbol);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[80vh] max-w-6xl">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-semibold">交易历史 - {selectedSymbol}</DialogTitle>
          <DialogDescription className="text-muted-foreground text-sm">
            查看该证券的所有交易记录，支持编辑和删除操作
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="-mx-6 flex-1 px-6">
          {filteredTransactions.length === 0 ? (
            <div className="flex h-32 items-center justify-center">
              <div className="text-center">
                <div className="text-muted-foreground text-sm">暂无交易记录</div>
                <div className="text-muted-foreground mt-1 text-xs">该证券还没有任何交易记录</div>
              </div>
            </div>
          ) : (
            <div className="bg-card rounded-lg border">
              <Table>
                <TableHeader>
                  <TableRow className="hover:bg-muted/50">
                    <TableHead className="font-medium">日期</TableHead>
                    <TableHead className="font-medium">类型</TableHead>
                    <TableHead className="text-right font-medium">数量</TableHead>
                    <TableHead className="text-right font-medium">价格</TableHead>
                    <TableHead className="text-right font-medium">金额</TableHead>
                    <TableHead className="text-right font-medium">手续费</TableHead>
                    <TableHead className="text-right font-medium">税费</TableHead>
                    <TableHead className="font-medium">备注</TableHead>
                    <TableHead className="w-16 text-center font-medium">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.map((transaction) => (
                    <TableRow key={transaction.id} className="hover:bg-muted/50 transition-colors">
                      <TableCell className="font-mono text-sm">{formatDate(transaction.time)}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={`${getTransactionTypeColor(transaction.type)} border-current`}
                        >
                          {getTransactionTypeName(transaction.type)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right font-mono">{transaction.shares.toLocaleString()}</TableCell>
                      <TableCell className="text-right font-mono">{formatCurrency(transaction.price)}</TableCell>
                      <TableCell className="text-right font-mono font-medium">
                        {formatCurrency(transaction.amount)}
                      </TableCell>
                      <TableCell className="text-muted-foreground text-right font-mono">
                        {formatCurrency(transaction.commission ?? 0)}
                      </TableCell>
                      <TableCell className="text-muted-foreground text-right font-mono">
                        {formatCurrency(transaction.tax ?? 0)}
                      </TableCell>
                      <TableCell className="text-muted-foreground max-w-32 truncate text-sm">
                        {transaction.comment || "-"}
                      </TableCell>
                      <TableCell className="text-center">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">打开菜单</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-32">
                            <DropdownMenuItem
                              onClick={() => onEditTransaction?.(transaction)}
                              className="cursor-pointer"
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              编辑
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => onDeleteTransaction?.(transaction.id)}
                              className="text-destructive focus:text-destructive cursor-pointer"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
