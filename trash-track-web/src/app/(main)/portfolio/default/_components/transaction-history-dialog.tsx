"use client";

import * as React from "react";
import { useState } from "react";
import { Edit, Trash2, MoreHorizontal, ChevronLeft, ChevronRight } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import {
  Transaction,
  formatCurrency,
  formatDate,
  getTransactionTypeName,
  getTransactionTypeColor,
} from "@/types/portfolio";

interface TransactionHistoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSymbol: string;
  transactions: Transaction[];
  onEditTransaction?: (transaction: Transaction) => void;
  onDeleteTransaction?: (transactionId: number) => void;
}

export function TransactionHistoryDialog({
  isOpen,
  onClose,
  selectedSymbol,
  transactions,
  onEditTransaction,
  onDeleteTransaction,
}: TransactionHistoryDialogProps) {
  const filteredTransactions = transactions.filter((transaction) => transaction.symbol === selectedSymbol);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 15;
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTransactions = filteredTransactions.slice(startIndex, endIndex);

  // 重置分页当对话框打开时
  React.useEffect(() => {
    if (isOpen) {
      setCurrentPage(1);
    }
  }, [isOpen, selectedSymbol]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-card max-h-[85vh] max-w-7 overflow-hidden">
        <DialogHeader>
          <DialogTitle>交易记录 - {selectedSymbol}</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col space-y-4 overflow-hidden">
          {filteredTransactions.length === 0 ? (
            <div className="flex h-32 items-center justify-center">
              <div className="text-center">
                <div className="text-muted-foreground text-sm">暂无交易记录</div>
                <div className="text-muted-foreground mt-1 text-xs">该证券还没有任何交易记录</div>
              </div>
            </div>
          ) : (
            <>
              {/* 表格容器 */}
              <div className="overflow-x-auto">
                <div className="bg-card rounded-lg border">
                  <Table>
                    <TableHeader>
                      <TableRow className="hover:bg-muted/50">
                        <TableHead className="w-24 font-medium">交易日期</TableHead>
                        <TableHead className="w-20 font-medium">类型</TableHead>
                        <TableHead className="w-32 font-medium">成交价/数量</TableHead>
                        <TableHead className="w-32 font-medium">税费/佣金</TableHead>
                        <TableHead className="w-28 text-right font-medium">成交金额</TableHead>
                        <TableHead className="w-32 font-medium">说明</TableHead>
                        <TableHead className="w-48 font-medium">备注</TableHead>
                        <TableHead className="w-20 text-center font-medium">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {currentTransactions.map((transaction) => (
                        <TableRow key={transaction.id} className="hover:bg-muted/50 transition-colors">
                          {/* 交易日期 */}
                          <TableCell className="font-mono text-sm">{formatDate(transaction.time)}</TableCell>

                          {/* 类型 */}
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={`${getTransactionTypeColor(transaction.type)} border-current text-xs`}
                            >
                              {getTransactionTypeName(transaction.type)}
                            </Badge>
                          </TableCell>

                          {/* 成交价/数量 */}
                          <TableCell className="font-mono text-sm">
                            <div className="space-y-1">
                              <div className="text-right">{formatCurrency(transaction.price)}</div>
                              <div className="text-muted-foreground text-right">
                                {transaction.shares.toLocaleString()}股
                              </div>
                            </div>
                          </TableCell>

                          {/* 税费/佣金 */}
                          <TableCell className="font-mono text-sm">
                            <div className="space-y-1">
                              <div className="text-muted-foreground text-right">
                                {formatCurrency(transaction.tax ?? 0)}
                              </div>
                              <div className="text-muted-foreground text-right">
                                {formatCurrency(transaction.commission ?? 0)}
                              </div>
                            </div>
                          </TableCell>

                          {/* 成交金额 */}
                          <TableCell className="text-right font-mono font-medium">
                            {formatCurrency(transaction.amount)}
                          </TableCell>

                          {/* 说明 */}
                          <TableCell className="text-sm">{transaction.name || selectedSymbol}</TableCell>

                          {/* 备注 */}
                          <TableCell className="w-48">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="text-muted-foreground cursor-help truncate text-sm">
                                    {transaction.comment || "-"}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="max-w-xs">{transaction.comment || "无备注"}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </TableCell>

                          {/* 操作 */}
                          <TableCell className="text-center">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">打开菜单</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-32">
                                <DropdownMenuItem
                                  onClick={() => onEditTransaction?.(transaction)}
                                  className="cursor-pointer"
                                >
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => onDeleteTransaction?.(transaction.id)}
                                  className="text-destructive focus:text-destructive cursor-pointer"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* 分页控件 */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-muted-foreground text-sm">
                    显示 {startIndex + 1}-{Math.min(endIndex, filteredTransactions.length)} 条， 共{" "}
                    {filteredTransactions.length} 条记录
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      上一页
                    </Button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => setCurrentPage(pageNum)}
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                    >
                      下一页
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
