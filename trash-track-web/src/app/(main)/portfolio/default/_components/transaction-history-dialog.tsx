"use client";

import * as React from "react";

import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface Transaction {
  id: number;
  symbol: string;
  name: string;
  type: number;
  time: number;
  shares: number;
  price: number;
  comment?: string;
  commission?: number;
  tax?: number;
  amount: number;
  created_at: string;
  updated_at: string;
}

interface TransactionHistoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSymbol: string;
  transactions: Transaction[];
}

export function TransactionHistoryDialog({
  isOpen,
  onClose,
  selectedSymbol,
  transactions,
}: TransactionHistoryDialogProps) {
  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString("zh-CN", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString("zh-CN");
  };

  const getTransactionType = (type: number) => {
    switch (type) {
      case 1:
        return "买入";
      case 2:
        return "卖出";
      case 3:
        return "分红";
      case 4:
        return "拆股";
      case 5:
        return "合股";
      case 6:
        return "除权除息";
      default:
        return "未知";
    }
  };

  const getTransactionTypeColor = (type: number) => {
    switch (type) {
      case 1:
        return "text-red-600";
      case 2:
        return "text-green-600";
      case 3:
        return "text-blue-600";
      default:
        return "text-gray-600";
    }
  };

  const filteredTransactions = transactions.filter((transaction) => transaction.symbol === selectedSymbol);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>交易历史 - {selectedSymbol}</DialogTitle>
          <DialogDescription>查看该证券的所有交易记录</DialogDescription>
        </DialogHeader>
        <div className="max-h-96 overflow-auto">
          {filteredTransactions.length === 0 ? (
            <div className="text-muted-foreground flex h-32 items-center justify-center">暂无交易记录</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>日期</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead className="text-right">数量</TableHead>
                  <TableHead className="text-right">价格</TableHead>
                  <TableHead className="text-right">金额</TableHead>
                  <TableHead className="text-right">手续费</TableHead>
                  <TableHead className="text-right">税费</TableHead>
                  <TableHead>备注</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTransactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell className="font-mono text-sm">{formatDate(transaction.time)}</TableCell>
                    <TableCell>
                      <span className={getTransactionTypeColor(transaction.type)}>
                        {getTransactionType(transaction.type)}
                      </span>
                    </TableCell>
                    <TableCell className="text-right font-mono">{transaction.shares.toLocaleString()}</TableCell>
                    <TableCell className="text-right font-mono">{formatCurrency(transaction.price)}</TableCell>
                    <TableCell className="text-right font-mono">{formatCurrency(transaction.amount)}</TableCell>
                    <TableCell className="text-right font-mono">
                      {formatCurrency(transaction.commission ?? 0)}
                    </TableCell>
                    <TableCell className="text-right font-mono">{formatCurrency(transaction.tax ?? 0)}</TableCell>
                    <TableCell className="text-muted-foreground text-sm">{transaction.comment ?? "-"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
