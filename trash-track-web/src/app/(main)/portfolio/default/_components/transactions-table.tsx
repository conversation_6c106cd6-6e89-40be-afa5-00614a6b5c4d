"use client";

import * as React from "react";

import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface Transaction {
  id: number;
  symbol: string;
  name: string;
  type: number;
  time: number;
  shares: number;
  price: number;
  comment?: string;
  commission?: number;
  tax?: number;
  amount: number;
  created_at: string;
  updated_at: string;
}

interface TransactionsTableProps {
  transactions: Transaction[];
  isLoading: boolean;
}

export function TransactionsTable({ transactions, isLoading }: TransactionsTableProps) {
  const formatCurrency = (amount: number, sign: string = "¥") => {
    return `${sign}${amount.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const getTransactionTypeName = (type: number) => {
    switch (type) {
      case 1:
        return "买入";
      case 2:
        return "卖出";
      case 3:
        return "分红";
      case 4:
        return "拆股";
      case 5:
        return "合股";
      case 6:
        return "除权除息";
      default:
        return "未知";
    }
  };

  const formatDateTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString("zh-CN");
  };

  if (isLoading) {
    return (
      <div className="p-8 text-center">
        <p className="text-muted-foreground">加载中...</p>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="p-8 text-center">
        <p className="text-muted-foreground">暂无交易记录</p>
      </div>
    );
  }

  return (
    <div className="bg-background/95 supports-[backdrop-filter]:bg-background/60 rounded-lg border backdrop-blur">
      <Table>
        <TableHeader>
          <TableRow className="hover:bg-muted/50">
            <TableHead className="font-medium">交易时间</TableHead>
            <TableHead className="font-medium">交易类型</TableHead>
            <TableHead className="font-medium">证券代码</TableHead>
            <TableHead className="font-medium">证券名称</TableHead>
            <TableHead className="text-right font-medium">数量</TableHead>
            <TableHead className="text-right font-medium">价格</TableHead>
            <TableHead className="text-right font-medium">金额</TableHead>
            <TableHead className="font-medium">备注</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction) => (
            <TableRow key={transaction.id} className="hover:bg-muted/50 transition-colors">
              <TableCell className="font-mono text-sm">{formatDateTime(transaction.time)}</TableCell>
              <TableCell>
                <Badge variant={transaction.type === 1 ? "default" : "secondary"}>
                  {getTransactionTypeName(transaction.type)}
                </Badge>
              </TableCell>
              <TableCell className="font-mono text-sm font-medium">{transaction.symbol}</TableCell>
              <TableCell>{transaction.name}</TableCell>
              <TableCell className="text-right font-mono">{transaction.shares.toLocaleString()}</TableCell>
              <TableCell className="text-right font-mono">{formatCurrency(transaction.price)}</TableCell>
              <TableCell className="text-right font-mono font-medium">{formatCurrency(transaction.amount)}</TableCell>
              <TableCell className="text-muted-foreground text-sm">{transaction.comment ?? "-"}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
