import { TransactionType, TransactionFormConfig, TransactionFormField } from "@/types/portfolio";

// 通用验证函数
const validateRequired = (value: any, fieldName: string) => {
  if (!value || (typeof value === "string" && !value.trim())) {
    return `${fieldName}不能为空`;
  }
  return "";
};

const validatePositiveNumber = (value: number, fieldName: string) => {
  if (value <= 0) {
    return `${fieldName}必须大于0`;
  }
  return "";
};

const validateNonNegativeNumber = (value: number, fieldName: string) => {
  if (value < 0) {
    return `${fieldName}不能为负数`;
  }
  return "";
};

// 买入交易配置
const buyTransactionConfig: TransactionFormConfig = {
  type: TransactionType.BUY,
  title: "买入股票",
  submitText: "确认买入",
  fields: [
    {
      name: "symbol",
      label: "品种代码",
      type: "text",
      required: true,
      placeholder: "如: 000001",
      validation: (value) => validateRequired(value, "品种代码"),
    },
    {
      name: "name",
      label: "品种名称",
      type: "text",
      required: true,
      placeholder: "如: 平安银行",
      validation: (value) => validateRequired(value, "品种名称"),
    },
    {
      name: "time",
      label: "委托日期",
      type: "date",
      required: true,
      validation: (value) => validateRequired(value, "委托日期"),
    },
    {
      name: "price",
      label: "买入价",
      type: "number",
      required: true,
      min: 0.01,
      step: 0.01,
      placeholder: "单价",
      validation: (value) => validatePositiveNumber(value, "买入价"),
    },
    {
      name: "shares",
      label: "买入量",
      type: "number",
      required: true,
      min: 1,
      step: 1,
      placeholder: "股数",
      validation: (value) => validatePositiveNumber(value, "买入量"),
    },
    {
      name: "commission",
      label: "佣金",
      type: "number",
      required: false,
      min: 0,
      step: 0.01,
      placeholder: "手续费",
      validation: (value) => value !== undefined ? validateNonNegativeNumber(value, "佣金") : "",
    },
    {
      name: "tax",
      label: "税费",
      type: "number",
      required: false,
      min: 0,
      step: 0.01,
      placeholder: "税费",
      validation: (value) => value !== undefined ? validateNonNegativeNumber(value, "税费") : "",
    },
    {
      name: "comment",
      label: "备注",
      type: "textarea",
      required: false,
      placeholder: "可选备注信息",
    },
  ],
  calculateAmount: (formData) => {
    const { shares = 0, price = 0, commission = 0, tax = 0 } = formData;
    return shares * price + commission + tax;
  },
};

// 卖出交易配置
const sellTransactionConfig: TransactionFormConfig = {
  type: TransactionType.SELL,
  title: "卖出股票",
  submitText: "确认卖出",
  fields: [
    {
      name: "symbol",
      label: "品种代码",
      type: "select", // 卖出时从持仓中选择
      required: true,
      placeholder: "请选择要卖出的证券",
      validation: (value) => validateRequired(value, "品种代码"),
    },
    {
      name: "time",
      label: "委托日期",
      type: "date",
      required: true,
      validation: (value) => validateRequired(value, "委托日期"),
    },
    {
      name: "price",
      label: "卖出价",
      type: "number",
      required: true,
      min: 0.01,
      step: 0.01,
      placeholder: "单价",
      validation: (value) => validatePositiveNumber(value, "卖出价"),
    },
    {
      name: "shares",
      label: "卖出量",
      type: "number",
      required: true,
      min: 1,
      step: 1,
      placeholder: "股数",
      validation: (value, formData) => {
        const error = validatePositiveNumber(value, "卖出量");
        if (error) return error;
        
        // 这里可以添加持仓数量验证逻辑
        return "";
      },
    },
    {
      name: "commission",
      label: "佣金",
      type: "number",
      required: false,
      min: 0,
      step: 0.01,
      placeholder: "手续费",
      validation: (value) => value !== undefined ? validateNonNegativeNumber(value, "佣金") : "",
    },
    {
      name: "tax",
      label: "税费",
      type: "number",
      required: false,
      min: 0,
      step: 0.01,
      placeholder: "税费",
      validation: (value) => value !== undefined ? validateNonNegativeNumber(value, "税费") : "",
    },
    {
      name: "comment",
      label: "备注",
      type: "textarea",
      required: false,
      placeholder: "可选备注信息",
    },
  ],
  calculateAmount: (formData) => {
    const { shares = 0, price = 0, commission = 0, tax = 0 } = formData;
    return shares * price - commission - tax;
  },
};

// 合股交易配置
const mergeTransactionConfig: TransactionFormConfig = {
  type: TransactionType.MERGE,
  title: "合股操作",
  submitText: "确认合股",
  fields: [
    {
      name: "symbol",
      label: "品种代码",
      type: "select",
      required: true,
      placeholder: "请选择要合股的证券",
      validation: (value) => validateRequired(value, "品种代码"),
    },
    {
      name: "time",
      label: "委托日期",
      type: "date",
      required: true,
      validation: (value) => validateRequired(value, "委托日期"),
    },
    {
      name: "unitShares",
      label: "多股合一",
      type: "number",
      required: true,
      min: 2,
      step: 1,
      placeholder: "多少股合为1股",
      validation: (value) => {
        const error = validatePositiveNumber(value, "合股比例");
        if (error) return error;
        if (value < 2) return "合股比例必须大于等于2";
        return "";
      },
    },
    {
      name: "comment",
      label: "备注",
      type: "textarea",
      required: false,
      placeholder: "可选备注信息",
    },
  ],
  calculateAmount: () => 0, // 合股不涉及金额
};

// 拆股交易配置
const splitTransactionConfig: TransactionFormConfig = {
  type: TransactionType.SPLIT,
  title: "拆股操作",
  submitText: "确认拆股",
  fields: [
    {
      name: "symbol",
      label: "品种代码",
      type: "select",
      required: true,
      placeholder: "请选择要拆股的证券",
      validation: (value) => validateRequired(value, "品种代码"),
    },
    {
      name: "time",
      label: "委托日期",
      type: "date",
      required: true,
      validation: (value) => validateRequired(value, "委托日期"),
    },
    {
      name: "unitShares",
      label: "每股拆为",
      type: "number",
      required: true,
      min: 2,
      step: 1,
      placeholder: "每1股拆为多少股",
      validation: (value) => {
        const error = validatePositiveNumber(value, "拆股比例");
        if (error) return error;
        if (value < 2) return "拆股比例必须大于等于2";
        return "";
      },
    },
    {
      name: "comment",
      label: "备注",
      type: "textarea",
      required: false,
      placeholder: "可选备注信息",
    },
  ],
  calculateAmount: () => 0, // 拆股不涉及金额
};

// 除权除息交易配置
const exRightsConfig: TransactionFormConfig = {
  type: TransactionType.EX_RIGHTS,
  title: "除权除息",
  submitText: "确认除权除息",
  fields: [
    {
      name: "symbol",
      label: "品种代码",
      type: "select",
      required: true,
      placeholder: "请选择证券",
      validation: (value) => validateRequired(value, "品种代码"),
    },
    {
      name: "time",
      label: "委托日期",
      type: "date",
      required: true,
      validation: (value) => validateRequired(value, "委托日期"),
    },
    {
      name: "unitIncreaseShares",
      label: "每10股转增",
      type: "number",
      required: false,
      min: 0,
      step: 0.01,
      placeholder: "转增股数",
      validation: (value) => value !== undefined ? validateNonNegativeNumber(value, "转增股数") : "",
    },
    {
      name: "unitBonusShares",
      label: "每10股送股",
      type: "number",
      required: false,
      min: 0,
      step: 0.01,
      placeholder: "送股股数",
      validation: (value) => value !== undefined ? validateNonNegativeNumber(value, "送股股数") : "",
    },
    {
      name: "unitDividend",
      label: "每10股红利",
      type: "number",
      required: false,
      min: 0,
      step: 0.01,
      placeholder: "红利金额",
      validation: (value) => value !== undefined ? validateNonNegativeNumber(value, "红利金额") : "",
    },
    {
      name: "tax",
      label: "税费",
      type: "number",
      required: false,
      min: 0,
      step: 0.01,
      placeholder: "税费",
      validation: (value) => value !== undefined ? validateNonNegativeNumber(value, "税费") : "",
    },
    {
      name: "comment",
      label: "备注",
      type: "textarea",
      required: false,
      placeholder: "可选备注信息",
    },
  ],
  calculateAmount: (formData) => {
    const { unitDividend = 0, tax = 0 } = formData;
    return unitDividend - tax; // 红利收入减去税费
  },
};

// 导出所有配置
export const transactionConfigs: Record<TransactionType, TransactionFormConfig> = {
  [TransactionType.BUY]: buyTransactionConfig,
  [TransactionType.SELL]: sellTransactionConfig,
  [TransactionType.MERGE]: mergeTransactionConfig,
  [TransactionType.SPLIT]: splitTransactionConfig,
  [TransactionType.EX_RIGHTS]: exRightsConfig,
  [TransactionType.DIVIDEND]: exRightsConfig, // 分红使用除权除息配置
};

// 获取交易类型配置
export function getTransactionConfig(type: TransactionType): TransactionFormConfig {
  return transactionConfigs[type];
}

// 获取所有支持的交易类型选项
export function getTransactionTypeOptions() {
  return [
    { value: TransactionType.BUY, label: "买入" },
    { value: TransactionType.SELL, label: "卖出" },
    { value: TransactionType.MERGE, label: "合股" },
    { value: TransactionType.SPLIT, label: "拆股" },
    { value: TransactionType.EX_RIGHTS, label: "除权除息" },
  ];
}
