"use client";

import * as React from "react";

import { MoreHori<PERSON><PERSON>, Eye, Trash2 } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { Holding, formatCurrency, formatPercentage } from "@/types/portfolio";

interface HoldingsTableProps {
  holdings: Holding[];
  onBuyStock: (symbol: string) => void;
  onSellStock: (symbol: string) => void;
  onViewTransactions: (symbol: string) => void;
  onDeleteHolding: (holdingId: number) => void;
}

export function HoldingsTable({
  holdings,
  onBuyStock,
  onSellStock,
  onViewTransactions,
  onDeleteHolding,
}: HoldingsTableProps) {
  if (holdings.length === 0) {
    return <div className="text-muted-foreground flex h-32 items-center justify-center">暂无持仓数据</div>;
  }

  return (
    <div className="bg-background/95 supports-[backdrop-filter]:bg-background/60 rounded-md border backdrop-blur">
      <Table>
        <TableHeader>
          <TableRow className="hover:bg-transparent">
            <TableHead className="font-medium">证券代码</TableHead>
            <TableHead className="font-medium">证券名称</TableHead>
            <TableHead className="text-right font-medium">持仓数量</TableHead>
            <TableHead className="text-right font-medium">当前价格</TableHead>
            <TableHead className="text-right font-medium">涨跌额</TableHead>
            <TableHead className="text-right font-medium">涨跌幅</TableHead>
            <TableHead className="text-right font-medium">持仓成本</TableHead>
            <TableHead className="text-right font-medium">市值</TableHead>
            <TableHead className="text-right font-medium">浮动盈亏</TableHead>
            <TableHead className="text-right font-medium">盈亏比例</TableHead>
            <TableHead className="text-center font-medium">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {holdings.map((holding) => (
            <TableRow key={holding.id} className="h-12">
              <TableCell className="font-mono text-sm">{holding.symbol}</TableCell>
              <TableCell className="font-medium">{holding.name}</TableCell>
              <TableCell className="text-right font-mono">{holding.shares.toLocaleString()}</TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(holding.current, holding.currency)}
              </TableCell>
              <TableCell className="text-right font-mono">
                <span className={holding.change >= 0 ? "text-red-600" : "text-green-600"}>
                  {holding.change >= 0 ? "+" : ""}
                  {formatCurrency(holding.change, holding.currency)}
                </span>
              </TableCell>
              <TableCell className="text-right font-mono">
                <Badge
                  variant={holding.percentage >= 0 ? "destructive" : "secondary"}
                  className={
                    holding.percentage >= 0
                      ? "bg-red-100 text-red-700 hover:bg-red-100"
                      : "bg-green-100 text-green-700 hover:bg-green-100"
                  }
                >
                  {formatPercentage(holding.percentage)}
                </Badge>
              </TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(holding.hold_cost, holding.currency)}
              </TableCell>
              <TableCell className="text-right font-mono">
                {formatCurrency(holding.market_value, holding.currency)}
              </TableCell>
              <TableCell className="text-right font-mono">
                <span className={holding.float_amount >= 0 ? "text-red-600" : "text-green-600"}>
                  {holding.float_amount >= 0 ? "+" : ""}
                  {formatCurrency(holding.float_amount, holding.currency)}
                </span>
              </TableCell>
              <TableCell className="text-right font-mono">
                <span className={holding.float_rate >= 0 ? "text-red-600" : "text-green-600"}>
                  {formatPercentage(holding.float_rate)}
                </span>
              </TableCell>
              <TableCell className="text-center">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">打开菜单</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onBuyStock(holding.symbol)}>买入</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onSellStock(holding.symbol)}>卖出</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onViewTransactions(holding.symbol)}>
                      <Eye className="mr-2 h-4 w-4" />
                      查看交易
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onDeleteHolding(holding.id)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
