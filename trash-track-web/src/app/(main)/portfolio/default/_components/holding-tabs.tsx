"use client";

import * as React from "react";

import { TrendingDown, DollarSign, Banknote } from "lucide-react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { useHoldings } from "../_hooks/use-holdings";

import { BuyStockForm } from "./buy-stock-form";
import { HoldingsTable } from "./holdings-table";
import { SellStockForm } from "./sell-stock-form";
import { TransactionHistoryDialog } from "./transaction-history-dialog";
import { TransactionsTable } from "./transactions-table";
import { TransferForm } from "./transfer-form";
import { TransfersTable } from "./transfers-table";

interface Portfolio {
  id: number;
  cash: number;
  sign: string;
  currency: string;
}

interface HoldingTabsProps {
  portfolioId?: number;
  portfolio?: Portfolio;
}

export function HoldingTabs({ portfolioId, portfolio }: HoldingTabsProps) {
  const { holdings, transactions, transfers, isLoading, buyStock, sellStock, bankTransfer, deleteHolding } =
    useHoldings(portfolioId);

  // Dialog states
  const [isBuyDialogOpen, setIsBuyDialogOpen] = React.useState(false);
  const [isSellDialogOpen, setIsSellDialogOpen] = React.useState(false);
  const [isTransferDialogOpen, setIsTransferDialogOpen] = React.useState(false);
  const [isTransactionHistoryOpen, setIsTransactionHistoryOpen] = React.useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);

  // Selected data states
  const [selectedSymbol, setSelectedSymbol] = React.useState<string>("");
  const [selectedHoldingId, setSelectedHoldingId] = React.useState<number | null>(null);

  // Event handlers
  const handleBuyClick = (symbol?: string) => {
    if (symbol) {
      setSelectedSymbol(symbol);
    }
    setIsBuyDialogOpen(true);
  };

  const handleSellClick = (symbol?: string) => {
    if (symbol) {
      setSelectedSymbol(symbol);
    }
    setIsSellDialogOpen(true);
  };

  const handleViewTransactions = (symbol: string) => {
    setSelectedSymbol(symbol);
    setIsTransactionHistoryOpen(true);
  };

  const handleDeleteHolding = (holdingId: number) => {
    setSelectedHoldingId(holdingId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteHolding = async () => {
    if (selectedHoldingId && portfolioId) {
      try {
        await deleteHolding(portfolioId, selectedHoldingId);
        setIsDeleteDialogOpen(false);
        setSelectedHoldingId(null);
      } catch (error) {
        console.error("Failed to delete holding:", error);
      }
    }
  };

  if (!portfolioId) {
    return (
      <div className="w-full p-8 text-center">
        <p className="text-muted-foreground">请选择一个投资组合查看持仓信息</p>
      </div>
    );
  }

  return (
    <>
      <Tabs defaultValue="holdings" className="w-full flex-col justify-start gap-6">
        <div className="flex items-center justify-between">
          <Label htmlFor="view-selector" className="sr-only">
            View
          </Label>
          <Select defaultValue="holdings">
            <SelectTrigger className="flex w-fit @4xl/main:hidden" size="sm" id="view-selector">
              <SelectValue placeholder="Select a view" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="holdings">持仓</SelectItem>
              <SelectItem value="transactions">交易记录</SelectItem>
              <SelectItem value="transfers">转账记录</SelectItem>
            </SelectContent>
          </Select>
          <TabsList className="**:data-[slot=badge]:bg-muted-foreground/30 hidden **:data-[slot=badge]:size-5 **:data-[slot=badge]:rounded-full **:data-[slot=badge]:px-1 @4xl/main:flex">
            <TabsTrigger value="holdings">持仓</TabsTrigger>
            <TabsTrigger value="transactions">
              交易记录 <Badge variant="secondary">{transactions.length}</Badge>
            </TabsTrigger>
            <TabsTrigger value="transfers">
              转账记录 <Badge variant="secondary">{transfers.length}</Badge>
            </TabsTrigger>
          </TabsList>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => handleBuyClick()}>
              <DollarSign className="h-4 w-4" />
              <span className="hidden lg:inline">买入</span>
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleSellClick()}>
              <TrendingDown className="h-4 w-4" />
              <span className="hidden lg:inline">卖出</span>
            </Button>
            <Button variant="outline" size="sm" onClick={() => setIsTransferDialogOpen(true)}>
              <Banknote className="h-4 w-4" />
              <span className="hidden lg:inline">银证转账</span>
            </Button>
          </div>
        </div>

        <TabsContent value="holdings" className="relative flex flex-col gap-4 overflow-auto">
          {isLoading ? (
            <div className="p-8 text-center">
              <p className="text-muted-foreground">加载中...</p>
            </div>
          ) : (
            <HoldingsTable
              holdings={holdings}
              onBuyStock={handleBuyClick}
              onSellStock={handleSellClick}
              onViewTransactions={handleViewTransactions}
              onDeleteHolding={handleDeleteHolding}
            />
          )}
        </TabsContent>

        <TabsContent value="transactions" className="flex flex-col gap-4">
          <TransactionsTable transactions={transactions} isLoading={isLoading} />
        </TabsContent>

        <TabsContent value="transfers" className="flex flex-col gap-4">
          <TransfersTable transfers={transfers} isLoading={isLoading} />
        </TabsContent>
      </Tabs>

      {/* 买入对话框 */}
      <Dialog open={isBuyDialogOpen} onOpenChange={setIsBuyDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>买入股票</DialogTitle>
            <DialogDescription>请填写买入股票的详细信息。</DialogDescription>
          </DialogHeader>
          <BuyStockForm
            onSubmit={async (data) => {
              if (portfolioId) {
                await buyStock(portfolioId, data);
                setIsBuyDialogOpen(false);
                setSelectedSymbol("");
              }
            }}
            onCancel={() => {
              setIsBuyDialogOpen(false);
              setSelectedSymbol("");
            }}
          />
        </DialogContent>
      </Dialog>

      {/* 卖出对话框 */}
      <Dialog open={isSellDialogOpen} onOpenChange={setIsSellDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>卖出股票</DialogTitle>
            <DialogDescription>请选择要卖出的股票并填写卖出信息。</DialogDescription>
          </DialogHeader>
          <SellStockForm
            holdings={holdings}
            onSubmit={async (data) => {
              if (portfolioId) {
                await sellStock(portfolioId, data);
                setIsSellDialogOpen(false);
                setSelectedSymbol("");
              }
            }}
            onCancel={() => {
              setIsSellDialogOpen(false);
              setSelectedSymbol("");
            }}
          />
        </DialogContent>
      </Dialog>

      {/* 银证转账对话框 */}
      <Dialog open={isTransferDialogOpen} onOpenChange={setIsTransferDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>银证转账</DialogTitle>
            <DialogDescription>在银行账户和证券账户之间转移资金。</DialogDescription>
          </DialogHeader>
          <TransferForm
            currentCash={portfolio?.cash ?? 0}
            currencySign={portfolio?.sign ?? "¥"}
            onSubmit={async (data) => {
              if (portfolioId) {
                await bankTransfer(portfolioId, data);
                setIsTransferDialogOpen(false);
              }
            }}
            onCancel={() => setIsTransferDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* 交易历史对话框 */}
      <TransactionHistoryDialog
        isOpen={isTransactionHistoryOpen}
        onClose={() => setIsTransactionHistoryOpen(false)}
        selectedSymbol={selectedSymbol}
        transactions={transactions}
      />

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>您确定要删除这个持仓记录吗？此操作无法撤销。</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setSelectedHoldingId(null);
              }}
            >
              取消
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteHolding} className="bg-red-600 hover:bg-red-700">
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
