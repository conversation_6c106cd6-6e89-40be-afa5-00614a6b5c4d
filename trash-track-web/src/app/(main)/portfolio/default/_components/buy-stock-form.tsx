"use client";

import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface BuyStockFormProps {
  onSubmit: (data: { symbol: string; name: string; shares: number; price: number; comment?: string }) => Promise<void>;
  onCancel?: () => void;
}

// eslint-disable-next-line complexity
export function BuyStockForm({ onSubmit, onCancel }: BuyStockFormProps) {
  const [formData, setFormData] = useState({
    symbol: "",
    name: "",
    shares: 0,
    price: 0,
    comment: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateSymbol = (symbol: string) => {
    if (!symbol.trim()) {
      return "证券代码不能为空";
    }
    return "";
  };

  const validateName = (name: string) => {
    if (!name.trim()) {
      return "证券名称不能为空";
    }
    return "";
  };

  const validateShares = (shares: number) => {
    if (shares <= 0) {
      return "买入数量必须大于0";
    }
    return "";
  };

  const validatePrice = (price: number) => {
    if (price <= 0) {
      return "买入价格必须大于0";
    }
    return "";
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    const symbolError = validateSymbol(formData.symbol);
    if (symbolError) newErrors.symbol = symbolError;

    const nameError = validateName(formData.name);
    if (nameError) newErrors.name = nameError;

    const sharesError = validateShares(formData.shares);
    if (sharesError) newErrors.shares = sharesError;

    const priceError = validatePrice(formData.price);
    if (priceError) newErrors.price = priceError;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      symbol: "",
      name: "",
      shares: 0,
      price: 0,
      comment: "",
    });
    setErrors({});
  };

  const prepareSubmissionData = () => ({
    symbol: formData.symbol.trim().toUpperCase(),
    name: formData.name.trim(),
    shares: formData.shares,
    price: formData.price,
    comment: formData.comment.trim() || undefined,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      const submissionData = prepareSubmissionData();
      await onSubmit(submissionData);
      resetForm();
    } catch (error) {
      console.error("Failed to buy stock:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalAmount = formData.shares * formData.price;

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="symbol">证券代码 *</Label>
        <Input
          id="symbol"
          type="text"
          value={formData.symbol}
          onChange={(e) => setFormData((prev) => ({ ...prev, symbol: e.target.value }))}
          placeholder="请输入证券代码，如 SH000001"
          required
        />
        {errors.symbol && <p className="text-sm text-red-500">{errors.symbol}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="name">证券名称 *</Label>
        <Input
          id="name"
          type="text"
          value={formData.name}
          onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
          placeholder="请输入证券名称"
          required
        />
        {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="shares">买入数量 *</Label>
          <Input
            id="shares"
            type="number"
            min="1"
            step="1"
            value={formData.shares || ""}
            onChange={(e) => setFormData((prev) => ({ ...prev, shares: parseInt(e.target.value) || 0 }))}
            placeholder="股数"
            required
          />
          {errors.shares && <p className="text-sm text-red-500">{errors.shares}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="price">买入价格 *</Label>
          <Input
            id="price"
            type="number"
            min="0.01"
            step="0.01"
            value={formData.price || ""}
            onChange={(e) => setFormData((prev) => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
            placeholder="单价"
            required
          />
          {errors.price && <p className="text-sm text-red-500">{errors.price}</p>}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="comment">备注</Label>
        <Textarea
          id="comment"
          value={formData.comment}
          onChange={(e) => setFormData((prev) => ({ ...prev, comment: e.target.value }))}
          placeholder="可选备注信息"
          rows={3}
        />
      </div>

      {totalAmount > 0 && (
        <div className="bg-muted rounded-lg p-3">
          <p className="text-muted-foreground text-sm">
            交易金额：
            <span className="text-foreground font-medium">
              ¥{totalAmount.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
          </p>
        </div>
      )}

      <div className="flex justify-end space-x-2 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            取消
          </Button>
        )}
        <Button
          type="submit"
          disabled={
            isSubmitting ||
            !formData.symbol.trim() ||
            !formData.name.trim() ||
            formData.shares <= 0 ||
            formData.price <= 0
          }
        >
          {isSubmitting ? "买入中..." : "确认买入"}
        </Button>
      </div>
    </form>
  );
}
