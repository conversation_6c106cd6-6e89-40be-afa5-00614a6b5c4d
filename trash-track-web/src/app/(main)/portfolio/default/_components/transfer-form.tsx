"use client";

import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface TransferFormProps {
  currentCash: number;
  currencySign: string;
  onSubmit: (data: { type: number; amount: number; comment?: string }) => Promise<void>;
  onCancel?: () => void;
}

// eslint-disable-next-line complexity
export function TransferForm({ currentCash, currencySign, onSubmit, onCancel }: TransferFormProps) {
  const [formData, setFormData] = useState({
    type: 1, // 1=转入，2=转出
    amount: 0,
    comment: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateAmount = (amount: number, type: number, availableCash: number) => {
    if (amount <= 0) {
      return "转账金额必须大于0";
    }
    if (type === 2 && amount > availableCash) {
      return `转出金额不能超过可用现金 ${currencySign}${availableCash.toLocaleString("zh-CN", { minimumFractionDigits: 2 })}`;
    }
    return "";
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    const amountError = validateAmount(formData.amount, formData.type, currentCash);
    if (amountError) newErrors.amount = amountError;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      type: 1,
      amount: 0,
      comment: "",
    });
    setErrors({});
  };

  const prepareSubmissionData = () => ({
    type: formData.type,
    amount: formData.amount,
    comment: formData.comment.trim() || undefined,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      const submissionData = prepareSubmissionData();
      await onSubmit(submissionData);
      resetForm();
    } catch (error) {
      console.error("Failed to transfer:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="type">转账类型 *</Label>
        <Select
          value={formData.type.toString()}
          onValueChange={(value) => setFormData((prev) => ({ ...prev, type: parseInt(value) }))}
        >
          <SelectTrigger>
            <SelectValue placeholder="请选择转账类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">银行转入证券账户</SelectItem>
            <SelectItem value="2">证券账户转出到银行</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="bg-muted rounded-lg p-3">
        <div className="text-sm">
          <span className="text-muted-foreground">当前可用现金：</span>
          <span className="text-foreground font-medium">
            {currencySign}
            {currentCash.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </span>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="amount">转账金额 *</Label>
        <Input
          id="amount"
          type="number"
          min="0.01"
          step="0.01"
          max={formData.type === 2 ? currentCash : undefined}
          value={formData.amount || ""}
          onChange={(e) => setFormData((prev) => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
          placeholder={`请输入转账金额 (${currencySign})`}
          required
        />
        {errors.amount && <p className="text-sm text-red-500">{errors.amount}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="comment">备注</Label>
        <Textarea
          id="comment"
          value={formData.comment}
          onChange={(e) => setFormData((prev) => ({ ...prev, comment: e.target.value }))}
          placeholder="可选备注信息"
          rows={3}
        />
      </div>

      {formData.amount > 0 && (
        <div className="bg-muted rounded-lg p-3">
          <div className="space-y-1 text-sm">
            <div>
              <span className="text-muted-foreground">转账方向：</span>
              <span className="text-foreground font-medium">
                {formData.type === 1 ? "银行 → 证券账户" : "证券账户 → 银行"}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">转账金额：</span>
              <span className="text-foreground font-medium">
                {currencySign}
                {formData.amount.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </span>
            </div>
            {formData.type === 1 && (
              <div>
                <span className="text-muted-foreground">转账后现金：</span>
                <span className="font-medium text-green-600">
                  {currencySign}
                  {(currentCash + formData.amount).toLocaleString("zh-CN", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </span>
              </div>
            )}
            {formData.type === 2 && (
              <div>
                <span className="text-muted-foreground">转账后现金：</span>
                <span className="font-medium text-orange-600">
                  {currencySign}
                  {(currentCash - formData.amount).toLocaleString("zh-CN", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-2 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            取消
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting || formData.amount <= 0}>
          {isSubmitting ? "转账中..." : `确认${formData.type === 1 ? "转入" : "转出"}`}
        </Button>
      </div>
    </form>
  );
}
