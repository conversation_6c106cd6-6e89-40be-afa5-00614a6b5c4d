"use client";

import { useState, useEffect } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

import { Transaction, TransactionType } from "@/types/portfolio";

interface EditTransactionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: Transaction | null;
  onSubmit: (transactionId: number, data: Partial<Transaction>) => Promise<void>;
}

export function EditTransactionDialog({
  isOpen,
  onClose,
  transaction,
  onSubmit,
}: EditTransactionDialogProps) {
  const [formData, setFormData] = useState({
    symbol: "",
    name: "",
    type: 1,
    time: 0,
    shares: 0,
    price: 0,
    comment: "",
    commission: 0,
    tax: 0,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // 当交易记录变化时更新表单数据
  useEffect(() => {
    if (transaction) {
      setFormData({
        symbol: transaction.symbol,
        name: transaction.name,
        type: transaction.type,
        time: transaction.time,
        shares: transaction.shares,
        price: transaction.price,
        comment: transaction.comment || "",
        commission: transaction.commission || 0,
        tax: transaction.tax || 0,
      });
    }
  }, [transaction]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.symbol.trim()) {
      newErrors.symbol = "证券代码不能为空";
    }

    if (!formData.name.trim()) {
      newErrors.name = "证券名称不能为空";
    }

    if (formData.shares <= 0) {
      newErrors.shares = "数量必须大于0";
    }

    if (formData.price <= 0) {
      newErrors.price = "价格必须大于0";
    }

    if (formData.commission < 0) {
      newErrors.commission = "手续费不能为负数";
    }

    if (formData.tax < 0) {
      newErrors.tax = "税费不能为负数";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!transaction || !validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // 计算交易金额
      const amount = formData.type === TransactionType.BUY 
        ? formData.shares * formData.price + formData.commission + formData.tax
        : formData.shares * formData.price - formData.commission - formData.tax;

      await onSubmit(transaction.id, {
        ...formData,
        amount,
      });
      
      onClose();
    } catch (error) {
      console.error("Failed to update transaction:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      symbol: "",
      name: "",
      type: 1,
      time: 0,
      shares: 0,
      price: 0,
      comment: "",
      commission: 0,
      tax: 0,
    });
    setErrors({});
    onClose();
  };

  const formatDateTimeLocal = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toISOString().slice(0, 16);
  };

  const parseDateTime = (dateTimeLocal: string) => {
    return new Date(dateTimeLocal).getTime();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>编辑交易记录</DialogTitle>
          <DialogDescription>
            修改交易记录信息，系统将重新计算相关数据
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="symbol">证券代码</Label>
              <Input
                id="symbol"
                value={formData.symbol}
                onChange={(e) => setFormData({ ...formData, symbol: e.target.value.toUpperCase() })}
                placeholder="如: 000001"
                className={errors.symbol ? "border-red-500" : ""}
              />
              {errors.symbol && <p className="text-sm text-red-500">{errors.symbol}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="name">证券名称</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="如: 平安银行"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">交易类型</Label>
            <Select
              value={formData.type.toString()}
              onValueChange={(value) => setFormData({ ...formData, type: parseInt(value) })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">买入</SelectItem>
                <SelectItem value="2">卖出</SelectItem>
                <SelectItem value="3">分红</SelectItem>
                <SelectItem value="4">拆股</SelectItem>
                <SelectItem value="5">合股</SelectItem>
                <SelectItem value="6">除权除息</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="time">交易时间</Label>
            <Input
              id="time"
              type="datetime-local"
              value={formatDateTimeLocal(formData.time)}
              onChange={(e) => setFormData({ ...formData, time: parseDateTime(e.target.value) })}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="shares">数量</Label>
              <Input
                id="shares"
                type="number"
                step="1"
                min="0"
                value={formData.shares}
                onChange={(e) => setFormData({ ...formData, shares: parseFloat(e.target.value) || 0 })}
                className={errors.shares ? "border-red-500" : ""}
              />
              {errors.shares && <p className="text-sm text-red-500">{errors.shares}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="price">价格</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                className={errors.price ? "border-red-500" : ""}
              />
              {errors.price && <p className="text-sm text-red-500">{errors.price}</p>}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="commission">手续费</Label>
              <Input
                id="commission"
                type="number"
                step="0.01"
                min="0"
                value={formData.commission}
                onChange={(e) => setFormData({ ...formData, commission: parseFloat(e.target.value) || 0 })}
                className={errors.commission ? "border-red-500" : ""}
              />
              {errors.commission && <p className="text-sm text-red-500">{errors.commission}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="tax">税费</Label>
              <Input
                id="tax"
                type="number"
                step="0.01"
                min="0"
                value={formData.tax}
                onChange={(e) => setFormData({ ...formData, tax: parseFloat(e.target.value) || 0 })}
                className={errors.tax ? "border-red-500" : ""}
              />
              {errors.tax && <p className="text-sm text-red-500">{errors.tax}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="comment">备注</Label>
            <Textarea
              id="comment"
              value={formData.comment}
              onChange={(e) => setFormData({ ...formData, comment: e.target.value })}
              placeholder="可选的备注信息"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "保存中..." : "保存"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
