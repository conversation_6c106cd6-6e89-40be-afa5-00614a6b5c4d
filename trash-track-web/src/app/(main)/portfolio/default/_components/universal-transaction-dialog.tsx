"use client";

import { useState, useEffect } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";

import { TransactionType, UniversalTransactionData, Holding, Portfolio } from "@/types/portfolio";
import { getTransactionConfig, getTransactionTypeOptions } from "./transaction-form-configs";
import { TransactionFormFieldComponent } from "./transaction-form-field";

interface UniversalTransactionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: UniversalTransactionData) => Promise<void>;
  portfolios: Portfolio[];
  holdings: Holding[];
  defaultType?: TransactionType;
  defaultSymbol?: string;
}

export function UniversalTransactionDialog({
  isOpen,
  onClose,
  onSubmit,
  portfolios,
  holdings,
  defaultType = TransactionType.BUY,
  defaultSymbol = "",
}: UniversalTransactionDialogProps) {
  const [transactionType, setTransactionType] = useState<TransactionType>(defaultType);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const config = getTransactionConfig(transactionType);
  const transactionTypeOptions = getTransactionTypeOptions();

  // 初始化表单数据
  useEffect(() => {
    const initialData: Record<string, any> = {
      type: transactionType,
      time: Date.now(),
      symbol: defaultSymbol,
    };

    // 如果有默认组合，设置默认组合ID
    if (portfolios.length > 0) {
      initialData.portfolioId = portfolios[0].id;
    }

    setFormData(initialData);
    setErrors({});
  }, [transactionType, defaultSymbol, portfolios]);

  // 获取证券选项（用于卖出、合股、拆股、除权除息）
  const getSecurityOptions = () => {
    if (transactionType === TransactionType.BUY) {
      return [];
    }
    return holdings.map((holding) => ({
      value: holding.symbol,
      label: `${holding.symbol} - ${holding.name} (持仓: ${holding.shares})`,
    }));
  };

  // 获取组合选项
  const getPortfolioOptions = () => {
    return portfolios.map((portfolio) => ({
      value: portfolio.id,
      label: `${portfolio.name} (${portfolio.market})`,
    }));
  };

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    config.fields.forEach((field) => {
      if (field.validation) {
        const error = field.validation(formData[field.name], formData);
        if (error) {
          newErrors[field.name] = error;
        }
      }
    });

    // 特殊验证：卖出时检查持仓数量
    if (transactionType === TransactionType.SELL && formData.symbol && formData.shares) {
      const holding = holdings.find((h) => h.symbol === formData.symbol);
      if (holding && formData.shares > holding.shares) {
        newErrors.shares = `卖出数量不能超过持仓数量 ${holding.shares}`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理字段值变化
  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData((prev) => ({ ...prev, [fieldName]: value }));

    // 清除该字段的错误
    if (errors[fieldName]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }

    // 特殊处理：选择证券时自动填充名称和价格
    if (fieldName === "symbol" && value) {
      const holding = holdings.find((h) => h.symbol === value);
      if (holding) {
        setFormData((prev) => ({
          ...prev,
          name: holding.name,
          price: transactionType === TransactionType.SELL ? holding.current : prev.price,
        }));
      }
    }
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // 计算交易金额
      const amount = config.calculateAmount ? config.calculateAmount(formData) : 0;

      const submissionData: UniversalTransactionData = {
        type: transactionType,
        symbol: formData.symbol?.toString().toUpperCase() || "",
        name: formData.name || "",
        portfolioId: formData.portfolioId,
        time: formData.time || Date.now(),
        shares: formData.shares || 0,
        price: formData.price || 0,
        commission: formData.commission || 0,
        tax: formData.tax || 0,
        comment: formData.comment || "",
        unitShares: formData.unitShares || 0,
        unitDividend: formData.unitDividend || 0,
        unitIncreaseShares: formData.unitIncreaseShares || 0,
        unitBonusShares: formData.unitBonusShares || 0,
      };

      await onSubmit(submissionData);
      handleClose();
    } catch (error) {
      console.error("Failed to submit transaction:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 关闭对话框
  const handleClose = () => {
    setFormData({});
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  // 计算并显示交易金额
  const calculateDisplayAmount = () => {
    if (!config.calculateAmount) return null;
    const amount = config.calculateAmount(formData);
    if (amount === 0) return null;
    return amount;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{config.title}</DialogTitle>
          <DialogDescription>
            请填写{config.title.toLowerCase()}的相关信息
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 交易类型选择 */}
          <div className="space-y-2">
            <Label htmlFor="transactionType">
              交易类型 <span className="text-red-500">*</span>
            </Label>
            <Select
              value={transactionType.toString()}
              onValueChange={(value) => setTransactionType(parseInt(value) as TransactionType)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {transactionTypeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 所属组合选择 */}
          <div className="space-y-2">
            <Label htmlFor="portfolioId">
              所属组合 <span className="text-red-500">*</span>
            </Label>
            <Select
              value={formData.portfolioId?.toString() || ""}
              onValueChange={(value) => handleFieldChange("portfolioId", parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="请选择组合" />
              </SelectTrigger>
              <SelectContent>
                {getPortfolioOptions().map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 动态渲染字段 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {config.fields.map((field) => {
              let fieldOptions: { value: string | number; label: string }[] = [];

              // 为选择字段提供选项
              if (field.type === "select") {
                if (field.name === "symbol") {
                  fieldOptions = getSecurityOptions();
                } else if (field.options) {
                  fieldOptions = field.options;
                }
              }

              return (
                <div
                  key={field.name}
                  className={
                    field.type === "textarea" || field.name === "symbol" || field.name === "name"
                      ? "md:col-span-2"
                      : ""
                  }
                >
                  <TransactionFormFieldComponent
                    field={field}
                    value={formData[field.name]}
                    onChange={(value) => handleFieldChange(field.name, value)}
                    error={errors[field.name]}
                    options={fieldOptions}
                  />
                </div>
              );
            })}
          </div>

          {/* 交易金额显示 */}
          {calculateDisplayAmount() !== null && (
            <div className="bg-muted rounded-lg p-3">
              <p className="text-muted-foreground text-sm">
                交易金额：
                <span className="text-foreground font-medium">
                  ¥{calculateDisplayAmount()?.toLocaleString("zh-CN", { 
                    minimumFractionDigits: 2, 
                    maximumFractionDigits: 2 
                  })}
                </span>
              </p>
            </div>
          )}

          {/* 提交按钮 */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "提交中..." : config.submitText}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
