"use client";

import { useState, useEffect } from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface Holding {
  id: number;
  symbol: string;
  name: string;
  shares: number;
  current: number;
  hold_cost: number;
  market_value: number;
}

interface SellStockFormProps {
  holdings: Holding[];
  onSubmit: (data: { symbol: string; shares: number; price: number; comment?: string }) => Promise<void>;
  onCancel?: () => void;
}

// eslint-disable-next-line complexity
export function SellStockForm({ holdings, onSubmit, onCancel }: SellStockFormProps) {
  const [formData, setFormData] = useState({
    symbol: "",
    shares: 0,
    price: 0,
    comment: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [selectedHolding, setSelectedHolding] = useState<Holding | null>(null);

  useEffect(() => {
    if (formData.symbol) {
      const holding = holdings.find((h) => h.symbol === formData.symbol);
      setSelectedHolding(holding ?? null);
      if (holding) {
        setFormData((prev) => ({ ...prev, price: holding.current }));
      }
    } else {
      setSelectedHolding(null);
    }
  }, [formData.symbol, holdings]);

  const validateSymbol = (symbol: string) => {
    if (!symbol) {
      return "请选择要卖出的证券";
    }
    return "";
  };

  const validateShares = (shares: number, holding: Holding | null) => {
    if (shares <= 0) {
      return "卖出数量必须大于0";
    }
    if (holding && shares > holding.shares) {
      return `卖出数量不能超过持仓数量 ${holding.shares}`;
    }
    return "";
  };

  const validatePrice = (price: number) => {
    if (price <= 0) {
      return "卖出价格必须大于0";
    }
    return "";
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    const symbolError = validateSymbol(formData.symbol);
    if (symbolError) newErrors.symbol = symbolError;

    const sharesError = validateShares(formData.shares, selectedHolding);
    if (sharesError) newErrors.shares = sharesError;

    const priceError = validatePrice(formData.price);
    if (priceError) newErrors.price = priceError;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      symbol: "",
      shares: 0,
      price: 0,
      comment: "",
    });
    setErrors({});
    setSelectedHolding(null);
  };

  const prepareSubmissionData = () => ({
    symbol: formData.symbol,
    shares: formData.shares,
    price: formData.price,
    comment: formData.comment.trim() || undefined,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      const submissionData = prepareSubmissionData();
      await onSubmit(submissionData);
      resetForm();
    } catch (error) {
      console.error("Failed to sell stock:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalAmount = formData.shares * formData.price;

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="symbol">选择证券 *</Label>
        <Select value={formData.symbol} onValueChange={(value) => setFormData((prev) => ({ ...prev, symbol: value }))}>
          <SelectTrigger>
            <SelectValue placeholder="请选择要卖出的证券" />
          </SelectTrigger>
          <SelectContent>
            {holdings.map((holding) => (
              <SelectItem key={holding.id} value={holding.symbol}>
                {holding.symbol} - {holding.name} (持仓: {holding.shares})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.symbol && <p className="text-sm text-red-500">{errors.symbol}</p>}
      </div>

      {selectedHolding && (
        <div className="bg-muted rounded-lg p-3">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">持仓数量：</span>
              <span className="font-medium">{selectedHolding.shares}</span>
            </div>
            <div>
              <span className="text-muted-foreground">持仓成本：</span>
              <span className="font-medium">¥{selectedHolding.hold_cost.toFixed(2)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">当前价格：</span>
              <span className="font-medium">¥{selectedHolding.current.toFixed(2)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">市值：</span>
              <span className="font-medium">
                ¥{selectedHolding.market_value.toLocaleString("zh-CN", { minimumFractionDigits: 2 })}
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="shares">卖出数量 *</Label>
          <Input
            id="shares"
            type="number"
            min="1"
            step="1"
            max={selectedHolding?.shares ?? undefined}
            value={formData.shares || ""}
            onChange={(e) => setFormData((prev) => ({ ...prev, shares: parseInt(e.target.value) || 0 }))}
            placeholder="股数"
            required
            disabled={!selectedHolding}
          />
          {errors.shares && <p className="text-sm text-red-500">{errors.shares}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="price">卖出价格 *</Label>
          <Input
            id="price"
            type="number"
            min="0.01"
            step="0.01"
            value={formData.price || ""}
            onChange={(e) => setFormData((prev) => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
            placeholder="单价"
            required
            disabled={!selectedHolding}
          />
          {errors.price && <p className="text-sm text-red-500">{errors.price}</p>}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="comment">备注</Label>
        <Textarea
          id="comment"
          value={formData.comment}
          onChange={(e) => setFormData((prev) => ({ ...prev, comment: e.target.value }))}
          placeholder="可选备注信息"
          rows={3}
        />
      </div>

      {totalAmount > 0 && (
        <div className="bg-muted rounded-lg p-3">
          <p className="text-muted-foreground text-sm">
            交易金额：
            <span className="text-foreground font-medium">
              ¥{totalAmount.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </span>
          </p>
        </div>
      )}

      <div className="flex justify-end space-x-2 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            取消
          </Button>
        )}
        <Button
          type="submit"
          disabled={isSubmitting || !formData.symbol || formData.shares <= 0 || formData.price <= 0}
        >
          {isSubmitting ? "卖出中..." : "确认卖出"}
        </Button>
      </div>
    </form>
  );
}
