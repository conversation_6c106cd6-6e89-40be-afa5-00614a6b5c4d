"use client";

import * as React from "react";

import { Plus, TrendingUp, TrendingDown } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { HoldingTabs } from "./holding-tabs";

import { Portfolio } from "@/types/portfolio";

interface PortfolioTabsProps {
  portfolios: Portfolio[];
  onAddPortfolio: () => void;
  isLoading?: boolean;
}

export function PortfolioTabs({ portfolios, onAddPortfolio, isLoading }: PortfolioTabsProps) {
  const [activeTab, setActiveTab] = React.useState<string>("");

  // Set active tab when portfolios change
  React.useEffect(() => {
    if (portfolios.length > 0 && !activeTab) {
      setActiveTab(portfolios[0].id.toString());
    }
  }, [portfolios, activeTab]);

  const formatCurrency = (amount: number, sign: string = "¥") => {
    return `${sign}${amount.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatPercentage = (rate: number) => {
    return `${(rate * 100).toFixed(2)}%`;
  };

  const getMarketName = (market: string) => {
    switch (market) {
      case "CHA":
        return "A股市场";
      case "US":
        return "美股市场";
      case "HK":
        return "港股市场";
      case "ALL":
        return "全球市场";
      default:
        return market;
    }
  };

  if (isLoading) {
    return (
      <div className="w-full p-8 text-center">
        <p className="text-muted-foreground">加载投资组合中...</p>
      </div>
    );
  }

  if (portfolios.length === 0) {
    return (
      <div className="w-full space-y-4 p-8 text-center">
        <p className="text-muted-foreground">您还没有创建任何投资组合</p>
        <Button onClick={onAddPortfolio}>
          <Plus className="mr-2 h-4 w-4" />
          创建第一个投资组合
        </Button>
      </div>
    );
  }

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full flex-col justify-start gap-6">
      <div className="flex items-center justify-between">
        <Label htmlFor="view-selector" className="sr-only">
          View
        </Label>
        <Select value={activeTab} onValueChange={setActiveTab}>
          <SelectTrigger className="flex w-fit @4xl/main:hidden" size="sm" id="view-selector">
            <SelectValue placeholder="选择投资组合" />
          </SelectTrigger>
          <SelectContent>
            {portfolios.map((portfolio) => (
              <SelectItem key={portfolio.id} value={portfolio.id.toString()}>
                {portfolio.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <TabsList className="**:data-[slot=badge]:bg-muted-foreground/30 hidden **:data-[slot=badge]:size-5 **:data-[slot=badge]:rounded-full **:data-[slot=badge]:px-1 @4xl/main:flex">
          {portfolios.map((portfolio) => (
            <TabsTrigger key={portfolio.id} value={portfolio.id.toString()}>
              {portfolio.name}
              {portfolio.accum_amount !== 0 && (
                <Badge variant="secondary" className={portfolio.accum_amount >= 0 ? "text-green-600" : "text-red-600"}>
                  {formatPercentage(portfolio.accum_rate)}
                </Badge>
              )}
            </TabsTrigger>
          ))}
        </TabsList>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={onAddPortfolio}>
            <Plus />
            <span className="hidden lg:inline">添加组合</span>
          </Button>
        </div>
      </div>

      {portfolios.map((portfolio) => (
        <TabsContent
          key={portfolio.id}
          value={portfolio.id.toString()}
          className="relative flex flex-col gap-4 overflow-auto"
        >
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">{portfolio.name}</h2>
                <p className="text-muted-foreground">{getMarketName(portfolio.market)}</p>
              </div>
            </div>

            {/* Portfolio Overview Cards */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">总资产</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(portfolio.assets, portfolio.sign)}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">市值</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(portfolio.market_value, portfolio.sign)}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">现金</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(portfolio.cash, portfolio.sign)}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">累计收益</CardTitle>
                  {portfolio.accum_amount >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                </CardHeader>
                <CardContent>
                  <div
                    className={`text-2xl font-bold ${portfolio.accum_amount >= 0 ? "text-green-600" : "text-red-600"}`}
                  >
                    {formatCurrency(portfolio.accum_amount, portfolio.sign)}
                  </div>
                  <p className={`text-xs ${portfolio.accum_rate >= 0 ? "text-green-600" : "text-red-600"}`}>
                    {formatPercentage(portfolio.accum_rate)}
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Portfolio Content */}
            <div className="w-full flex-1">
              <HoldingTabs
                portfolioId={portfolio.id}
                portfolio={{
                  id: portfolio.id,
                  cash: portfolio.cash,
                  sign: portfolio.sign,
                  currency: portfolio.currency,
                }}
                portfolios={portfolios}
              />
            </div>
          </div>
        </TabsContent>
      ))}
    </Tabs>
  );
}
