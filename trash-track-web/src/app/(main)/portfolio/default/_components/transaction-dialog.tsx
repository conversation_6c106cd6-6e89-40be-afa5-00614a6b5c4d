"use client";

import { useState, useEffect } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";

import {
  TransactionType,
  UniversalTransactionData,
  Transaction,
  Holding,
  Portfolio,
  getTransactionTypeName,
} from "@/types/portfolio";

interface TransactionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: UniversalTransactionData) => Promise<void>;
  portfolios: Portfolio[];
  holdings: Holding[];
  mode: "create" | "edit";
  defaultType?: TransactionType;
  defaultSymbol?: string;
  editTransaction?: Transaction | null;
  allowTypeChange?: boolean;
}

export function TransactionDialog({
  isOpen,
  onClose,
  onSubmit,
  portfolios,
  holdings,
  mode = "create",
  defaultType = TransactionType.BUY,
  defaultSymbol = "",
  editTransaction = null,
  allowTypeChange = true,
}: TransactionDialogProps) {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (mode === "edit" && editTransaction) {
      // 编辑模式：使用现有交易数据
      setFormData({
        type: editTransaction.type,
        symbol: editTransaction.symbol,
        name: editTransaction.name,
        portfolioId: portfolios.find((p) => p.id === editTransaction.portfolio_id)?.id || portfolios[0]?.id,
        time: new Date(editTransaction.time),
        shares: editTransaction.shares,
        price: editTransaction.price,
        commission: editTransaction.commission || 0,
        tax: editTransaction.tax || 0,
        comment: editTransaction.comment || "",
      });
    } else {
      // 创建模式：使用默认值
      const initialData: Record<string, any> = {
        type: defaultType,
        symbol: defaultSymbol,
        name: "",
        time: new Date(),
        shares: 0,
        price: 0,
        commission: 0,
        tax: 0,
        comment: "",
      };

      if (portfolios.length > 0) {
        initialData.portfolioId = portfolios[0].id;
      }

      setFormData(initialData);
    }
    setErrors({});
  }, [mode, editTransaction, defaultType, defaultSymbol, portfolios, isOpen]);

  // 获取当前交易类型
  const currentType = formData.type || defaultType;

  // 获取证券选项（用于卖出、合股、拆股、除权除息）
  const getSecurityOptions = () => {
    if (currentType === TransactionType.BUY) {
      return [];
    }
    return holdings.map((holding) => ({
      value: holding.symbol,
      label: `${holding.symbol} - ${holding.name} (持仓: ${holding.shares})`,
    }));
  };

  // 获取组合选项
  const getPortfolioOptions = () => {
    return portfolios.map((portfolio) => ({
      value: portfolio.id,
      label: `${portfolio.name} (${portfolio.market})`,
    }));
  };

  // 获取交易类型选项
  const getTransactionTypeOptions = () => {
    return [
      { value: TransactionType.BUY, label: "买入" },
      { value: TransactionType.SELL, label: "卖出" },
      { value: TransactionType.MERGE, label: "合股" },
      { value: TransactionType.SPLIT, label: "拆股" },
      { value: TransactionType.EX_RIGHTS, label: "除权除息" },
    ];
  };

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.symbol?.trim()) {
      newErrors.symbol = "品种代码不能为空";
    }

    if (!formData.portfolioId) {
      newErrors.portfolioId = "请选择所属组合";
    }

    if (!formData.time) {
      newErrors.time = "请选择委托日期";
    }

    // 根据交易类型验证特定字段
    switch (currentType) {
      case TransactionType.BUY:
      case TransactionType.SELL:
        if (!formData.name?.trim()) {
          newErrors.name = "品种名称不能为空";
        }
        if (!formData.price || formData.price <= 0) {
          newErrors.price = "价格必须大于0";
        }
        if (!formData.shares || formData.shares <= 0) {
          newErrors.shares = "数量必须大于0";
        }
        if (formData.commission < 0) {
          newErrors.commission = "佣金不能为负数";
        }
        if (formData.tax < 0) {
          newErrors.tax = "税费不能为负数";
        }
        break;

      case TransactionType.MERGE:
      case TransactionType.SPLIT:
        if (!formData.unitShares || formData.unitShares < 2) {
          newErrors.unitShares = "比例必须大于等于2";
        }
        break;

      case TransactionType.EX_RIGHTS:
        if (formData.unitIncreaseShares < 0) {
          newErrors.unitIncreaseShares = "转增股数不能为负数";
        }
        if (formData.unitBonusShares < 0) {
          newErrors.unitBonusShares = "送股股数不能为负数";
        }
        if (formData.unitDividend < 0) {
          newErrors.unitDividend = "红利金额不能为负数";
        }
        if (formData.tax < 0) {
          newErrors.tax = "税费不能为负数";
        }
        break;
    }

    // 特殊验证：卖出时检查持仓数量
    if (currentType === TransactionType.SELL && formData.symbol && formData.shares) {
      const holding = holdings.find((h) => h.symbol === formData.symbol);
      if (holding && formData.shares > holding.shares) {
        newErrors.shares = `卖出数量不能超过持仓数量 ${holding.shares}`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理字段值变化
  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData((prev) => ({ ...prev, [fieldName]: value }));

    // 清除该字段的错误
    if (errors[fieldName]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }

    // 特殊处理：选择证券时自动填充名称和价格
    if (fieldName === "symbol" && value) {
      const holding = holdings.find((h) => h.symbol === value);
      if (holding) {
        setFormData((prev) => ({
          ...prev,
          name: holding.name,
          price: currentType === TransactionType.SELL ? holding.current : prev.price,
        }));
      }
    }
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const submissionData: UniversalTransactionData = {
        type: currentType,
        symbol: formData.symbol?.toString().toUpperCase() || "",
        name: formData.name || "",
        portfolioId: formData.portfolioId,
        time: formData.time ? formData.time.getTime() : Date.now(),
        shares: formData.shares || 0,
        price: formData.price || 0,
        commission: formData.commission || 0,
        tax: formData.tax || 0,
        comment: formData.comment || "",
        unitShares: formData.unitShares || 0,
        unitDividend: formData.unitDividend || 0,
        unitIncreaseShares: formData.unitIncreaseShares || 0,
        unitBonusShares: formData.unitBonusShares || 0,
      };

      await onSubmit(submissionData);
      handleClose();
    } catch (error) {
      console.error("Failed to submit transaction:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 关闭对话框
  const handleClose = () => {
    setFormData({});
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-card max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>交易</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 交易类型选择 */}
          <div className="flex items-center gap-4">
            <Label htmlFor="transactionType" className="w-20 text-right">
              交易类型 <span className="text-red-500">*</span>
            </Label>
            <div className="flex-1">
              <Select
                value={currentType.toString()}
                onValueChange={(value) => handleFieldChange("type", parseInt(value) as TransactionType)}
                disabled={!allowTypeChange}
              >
                <SelectTrigger className={errors.type ? "border-red-500" : ""}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {getTransactionTypeOptions().map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.type && <p className="mt-1 text-sm text-red-500">{errors.type}</p>}
            </div>
          </div>

          {/* 品种代码 */}
          <div className="flex items-center gap-4">
            <Label htmlFor="symbol" className="w-20 text-right">
              品种代码 <span className="text-red-500">*</span>
            </Label>
            <div className="flex-1">
              {currentType === TransactionType.BUY ? (
                <Input
                  id="symbol"
                  value={formData.symbol || ""}
                  onChange={(e) => handleFieldChange("symbol", e.target.value.toUpperCase())}
                  placeholder="如: 000001"
                  className={errors.symbol ? "border-red-500" : ""}
                />
              ) : (
                <Select value={formData.symbol || ""} onValueChange={(value) => handleFieldChange("symbol", value)}>
                  <SelectTrigger className={errors.symbol ? "border-red-500" : ""}>
                    <SelectValue placeholder="请选择证券" />
                  </SelectTrigger>
                  <SelectContent>
                    {getSecurityOptions().map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              {errors.symbol && <p className="mt-1 text-sm text-red-500">{errors.symbol}</p>}
            </div>
          </div>

          {/* 所属组合 */}
          <div className="flex items-center gap-4">
            <Label htmlFor="portfolioId" className="w-20 text-right">
              所属组合 <span className="text-red-500">*</span>
            </Label>
            <div className="flex-1">
              <Select
                value={formData.portfolioId?.toString() || ""}
                onValueChange={(value) => handleFieldChange("portfolioId", parseInt(value))}
              >
                <SelectTrigger className={errors.portfolioId ? "border-red-500" : ""}>
                  <SelectValue placeholder="请选择组合" />
                </SelectTrigger>
                <SelectContent>
                  {getPortfolioOptions().map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.portfolioId && <p className="mt-1 text-sm text-red-500">{errors.portfolioId}</p>}
            </div>
          </div>

          {/* 委托日期 */}
          <div className="flex items-center gap-4">
            <Label htmlFor="time" className="w-20 text-right">
              委托日期 <span className="text-red-500">*</span>
            </Label>
            <div className="flex-1">
              <DatePicker
                date={formData.time}
                onDateChange={(date) => handleFieldChange("time", date)}
                placeholder="选择委托日期"
                className={errors.time ? "border-red-500" : ""}
              />
              {errors.time && <p className="mt-1 text-sm text-red-500">{errors.time}</p>}
            </div>
          </div>

          {/* 动态字段渲染 */}
          {/* 品种名称 - 买入和卖出需要 */}
          {(currentType === TransactionType.BUY || currentType === TransactionType.SELL) && (
            <div className="flex items-center gap-4">
              <Label htmlFor="name" className="w-20 text-right">
                品种名称 <span className="text-red-500">*</span>
              </Label>
              <div className="flex-1">
                <Input
                  id="name"
                  value={formData.name || ""}
                  onChange={(e) => handleFieldChange("name", e.target.value)}
                  placeholder="如: 平安银行"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
              </div>
            </div>
          )}

          {/* 价格 - 买入和卖出需要 */}
          {(currentType === TransactionType.BUY || currentType === TransactionType.SELL) && (
            <div className="flex items-center gap-4">
              <Label htmlFor="price" className="w-20 text-right">
                {currentType === TransactionType.BUY ? "买入价" : "卖出价"} <span className="text-red-500">*</span>
              </Label>
              <div className="flex-1">
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.price || ""}
                  onChange={(e) => handleFieldChange("price", parseFloat(e.target.value) || 0)}
                  placeholder="单价"
                  className={errors.price ? "border-red-500" : ""}
                />
                {errors.price && <p className="mt-1 text-sm text-red-500">{errors.price}</p>}
              </div>
            </div>
          )}

          {/* 数量 - 买入和卖出需要 */}
          {(currentType === TransactionType.BUY || currentType === TransactionType.SELL) && (
            <div className="flex items-center gap-4">
              <Label htmlFor="shares" className="w-20 text-right">
                {currentType === TransactionType.BUY ? "买入量" : "卖出量"} <span className="text-red-500">*</span>
              </Label>
              <div className="flex-1">
                <Input
                  id="shares"
                  type="number"
                  step="1"
                  min="0"
                  value={formData.shares || ""}
                  onChange={(e) => handleFieldChange("shares", parseInt(e.target.value) || 0)}
                  placeholder="股数"
                  className={errors.shares ? "border-red-500" : ""}
                />
                {errors.shares && <p className="mt-1 text-sm text-red-500">{errors.shares}</p>}
              </div>
            </div>
          )}

          {/* 佣金 - 买入和卖出需要 */}
          {(currentType === TransactionType.BUY || currentType === TransactionType.SELL) && (
            <div className="flex items-center gap-4">
              <Label htmlFor="commission" className="w-20 text-right">
                佣金
              </Label>
              <div className="flex-1">
                <Input
                  id="commission"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.commission || ""}
                  onChange={(e) => handleFieldChange("commission", parseFloat(e.target.value) || 0)}
                  placeholder="手续费"
                  className={errors.commission ? "border-red-500" : ""}
                />
                {errors.commission && <p className="mt-1 text-sm text-red-500">{errors.commission}</p>}
              </div>
            </div>
          )}

          {/* 税费 - 买入、卖出、除权除息需要 */}
          {(currentType === TransactionType.BUY ||
            currentType === TransactionType.SELL ||
            currentType === TransactionType.EX_RIGHTS) && (
            <div className="flex items-center gap-4">
              <Label htmlFor="tax" className="w-20 text-right">
                税费
              </Label>
              <div className="flex-1">
                <Input
                  id="tax"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.tax || ""}
                  onChange={(e) => handleFieldChange("tax", parseFloat(e.target.value) || 0)}
                  placeholder="税费"
                  className={errors.tax ? "border-red-500" : ""}
                />
                {errors.tax && <p className="mt-1 text-sm text-red-500">{errors.tax}</p>}
              </div>
            </div>
          )}

          {/* 多股合一 - 合股需要 */}
          {currentType === TransactionType.MERGE && (
            <div className="flex items-center gap-4">
              <Label htmlFor="unitShares" className="w-20 text-right">
                多股合一 <span className="text-red-500">*</span>
              </Label>
              <div className="flex-1">
                <Input
                  id="unitShares"
                  type="number"
                  step="1"
                  min="2"
                  value={formData.unitShares || ""}
                  onChange={(e) => handleFieldChange("unitShares", parseInt(e.target.value) || 0)}
                  placeholder="多少股合为1股"
                  className={errors.unitShares ? "border-red-500" : ""}
                />
                {errors.unitShares && <p className="mt-1 text-sm text-red-500">{errors.unitShares}</p>}
              </div>
            </div>
          )}

          {/* 每股拆为 - 拆股需要 */}
          {currentType === TransactionType.SPLIT && (
            <div className="flex items-center gap-4">
              <Label htmlFor="unitShares" className="w-20 text-right">
                每股拆为 <span className="text-red-500">*</span>
              </Label>
              <div className="flex-1">
                <Input
                  id="unitShares"
                  type="number"
                  step="1"
                  min="2"
                  value={formData.unitShares || ""}
                  onChange={(e) => handleFieldChange("unitShares", parseInt(e.target.value) || 0)}
                  placeholder="每1股拆为多少股"
                  className={errors.unitShares ? "border-red-500" : ""}
                />
                {errors.unitShares && <p className="mt-1 text-sm text-red-500">{errors.unitShares}</p>}
              </div>
            </div>
          )}

          {/* 除权除息字段 */}
          {currentType === TransactionType.EX_RIGHTS && (
            <>
              <div className="flex items-center gap-4">
                <Label htmlFor="unitIncreaseShares" className="w-20 text-right">
                  每10股转增
                </Label>
                <div className="flex-1">
                  <Input
                    id="unitIncreaseShares"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.unitIncreaseShares || ""}
                    onChange={(e) => handleFieldChange("unitIncreaseShares", parseFloat(e.target.value) || 0)}
                    placeholder="转增股数"
                    className={errors.unitIncreaseShares ? "border-red-500" : ""}
                  />
                  {errors.unitIncreaseShares && (
                    <p className="mt-1 text-sm text-red-500">{errors.unitIncreaseShares}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Label htmlFor="unitBonusShares" className="w-20 text-right">
                  每10股送股
                </Label>
                <div className="flex-1">
                  <Input
                    id="unitBonusShares"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.unitBonusShares || ""}
                    onChange={(e) => handleFieldChange("unitBonusShares", parseFloat(e.target.value) || 0)}
                    placeholder="送股股数"
                    className={errors.unitBonusShares ? "border-red-500" : ""}
                  />
                  {errors.unitBonusShares && <p className="mt-1 text-sm text-red-500">{errors.unitBonusShares}</p>}
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Label htmlFor="unitDividend" className="w-20 text-right">
                  每10股红利
                </Label>
                <div className="flex-1">
                  <Input
                    id="unitDividend"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.unitDividend || ""}
                    onChange={(e) => handleFieldChange("unitDividend", parseFloat(e.target.value) || 0)}
                    placeholder="红利金额"
                    className={errors.unitDividend ? "border-red-500" : ""}
                  />
                  {errors.unitDividend && <p className="mt-1 text-sm text-red-500">{errors.unitDividend}</p>}
                </div>
              </div>
            </>
          )}

          {/* 备注 - 所有类型都需要 */}
          <div className="flex items-start gap-4">
            <Label htmlFor="comment" className="w-20 pt-2 text-right">
              备注
            </Label>
            <div className="flex-1">
              <Textarea
                id="comment"
                value={formData.comment || ""}
                onChange={(e) => handleFieldChange("comment", e.target.value)}
                placeholder="可选备注信息"
                rows={3}
                className={errors.comment ? "border-red-500" : ""}
              />
              {errors.comment && <p className="mt-1 text-sm text-red-500">{errors.comment}</p>}
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "提交中..." : "确认"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
