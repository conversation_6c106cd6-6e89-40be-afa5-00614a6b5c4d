"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

import { TransactionFormField } from "@/types/portfolio";

interface TransactionFormFieldProps {
  field: TransactionFormField;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  disabled?: boolean;
  options?: { value: string | number; label: string }[];
}

export function TransactionFormFieldComponent({
  field,
  value,
  onChange,
  error,
  disabled = false,
  options = [],
}: TransactionFormFieldProps) {
  const { name, label, type, required, placeholder, min, max, step } = field;

  const renderField = () => {
    switch (type) {
      case "text":
        return (
          <Input
            id={name}
            type="text"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            disabled={disabled}
            className={error ? "border-red-500" : ""}
          />
        );

      case "number":
        return (
          <Input
            id={name}
            type="number"
            min={min}
            max={max}
            step={step}
            value={value || ""}
            onChange={(e) => {
              const numValue = parseFloat(e.target.value);
              onChange(isNaN(numValue) ? 0 : numValue);
            }}
            placeholder={placeholder}
            disabled={disabled}
            className={error ? "border-red-500" : ""}
          />
        );

      case "date":
        return (
          <Input
            id={name}
            type="datetime-local"
            value={value ? new Date(value).toISOString().slice(0, 16) : ""}
            onChange={(e) => {
              const dateValue = new Date(e.target.value).getTime();
              onChange(isNaN(dateValue) ? Date.now() : dateValue);
            }}
            disabled={disabled}
            className={error ? "border-red-500" : ""}
          />
        );

      case "select":
        return (
          <Select
            value={value?.toString() || ""}
            onValueChange={(selectedValue) => {
              // 尝试转换为数字，如果失败则保持字符串
              const numValue = parseFloat(selectedValue);
              onChange(isNaN(numValue) ? selectedValue : numValue);
            }}
            disabled={disabled}
          >
            <SelectTrigger className={error ? "border-red-500" : ""}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "textarea":
        return (
          <Textarea
            id={name}
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            disabled={disabled}
            rows={3}
            className={error ? "border-red-500" : ""}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={name}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {renderField()}
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
}
