"use client";

import { useState, useEffect } from "react";

import { useRouter } from "next/navigation";

import { Loader2 } from "lucide-react";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import { useAuth } from "../_hooks/use-auth";
import { usePortfolios } from "../_hooks/use-portfolios";

import { CreatePortfolioForm } from "./create-portfolio-form";
import { PortfolioTabs } from "./portfolio-tabs";

export function PortfolioPage() {
  const { user, isInitializing } = useAuth();
  const { portfolios, isLoading, createPortfolio, refreshPortfolios } = usePortfolios();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const router = useRouter();

  // Handle redirect to login page when user is not authenticated
  useEffect(() => {
    if (!isInitializing && !user) {
      router.push("/auth/v1/login");
    }
  }, [isInitializing, user, router]);

  useEffect(() => {
    if (user) {
      refreshPortfolios();
    }
  }, [user, refreshPortfolios]);

  // Show loading spinner during initialization
  if (isInitializing) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">加载中...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show loading or redirect message if not authenticated
  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">重定向到登录页面...</p>
          </div>
        </div>
      </div>
    );
  }

  const handleCreatePortfolio = async (data: {
    market: string;
    name: string;
    assets?: number;
    principal?: number;
    cash?: number;
    sign?: string;
    currency?: string;
  }) => {
    try {
      await createPortfolio(data);
      setIsCreateDialogOpen(false);
      // Refresh portfolios to get the latest data
      await refreshPortfolios();
    } catch (error) {
      console.error("Failed to create portfolio:", error);
    }
  };

  const handleAddPortfolio = () => {
    setIsCreateDialogOpen(true);
  };

  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      <PortfolioTabs portfolios={portfolios} onAddPortfolio={handleAddPortfolio} isLoading={isLoading} />

      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>创建新的投资组合</DialogTitle>
            <DialogDescription>请填写投资组合的基本信息。</DialogDescription>
          </DialogHeader>
          <CreatePortfolioForm onSubmit={handleCreatePortfolio} onCancel={() => setIsCreateDialogOpen(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
}
