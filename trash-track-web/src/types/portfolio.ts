/**
 * 组合持仓相关类型定义
 * 统一管理Portfolio、Holding、Transaction、BankTransfer等类型
 */

// 基础组合类型
export interface Portfolio {
  id: number;
  user_id: number;
  market: string;
  name: string;
  assets: number;
  principal: number;
  cash: number;
  sign: string;
  currency: string;
  market_value: number;
  float_amount: number;
  float_rate: number;
  accum_amount: number;
  accum_rate: number;
  day_float_amount: number;
  day_float_rate: number;
  created_at: string;
  updated_at: string;
}

// 创建组合数据类型
export interface CreatePortfolioData {
  market: string;
  name: string;
  assets?: number;
  principal?: number;
  cash?: number;
  sign?: string;
  currency?: string;
}

// 持仓类型
export interface Holding {
  id: number;
  portfolio_id: number;
  symbol: string;
  name: string;
  shares: number;
  current: number;
  change: number;
  percentage: number;
  currency: string;
  diluted_cost: number;
  hold_cost: number;
  market_value: number;
  float_amount: number;
  float_rate: number;
  accum_amount: number;
  accum_rate: number;
  day_float_amount: number;
  day_float_rate: number;
  created_at: string;
  updated_at: string;
}

// 交易记录类型
export interface Transaction {
  id: number;
  portfolio_id: number;
  symbol: string;
  name: string;
  type: number; // 1=买入，2=卖出，3=分红，4=拆股，5=合股，6=除权除息
  time: number;
  shares: number;
  price: number;
  comment?: string;
  commission?: number;
  tax?: number;
  amount: number;
  created_at: string;
  updated_at: string;
}

// 银证转账类型
export interface BankTransfer {
  id: number;
  portfolio_id: number;
  type: number; // 1=转入，2=转出
  market: string;
  amount: number;
  time: number;
  comment?: string;
  created_at: string;
  updated_at: string;
}

// 买入交易数据类型
export interface BuyTransactionData {
  symbol: string;
  name: string;
  shares: number;
  price: number;
  comment?: string;
}

// 卖出交易数据类型
export interface SellTransactionData {
  symbol: string;
  shares: number;
  price: number;
  comment?: string;
}

// 转账数据类型
export interface TransferData {
  type: number; // 1=转入，2=转出
  amount: number;
  comment?: string;
}

// 交易类型枚举
export enum TransactionType {
  BUY = 1,
  SELL = 2,
  DIVIDEND = 3,
  SPLIT = 4,
  MERGE = 5,
  EX_RIGHTS = 6,
}

// 转账类型枚举
export enum TransferType {
  DEPOSIT = 1,
  WITHDRAW = 2,
}

// 交易类型名称映射
export const TRANSACTION_TYPE_NAMES: Record<TransactionType, string> = {
  [TransactionType.BUY]: "买入",
  [TransactionType.SELL]: "卖出",
  [TransactionType.DIVIDEND]: "分红",
  [TransactionType.SPLIT]: "拆股",
  [TransactionType.MERGE]: "合股",
  [TransactionType.EX_RIGHTS]: "除权除息",
};

// 转账类型名称映射
export const TRANSFER_TYPE_NAMES: Record<TransferType, string> = {
  [TransferType.DEPOSIT]: "转入",
  [TransferType.WITHDRAW]: "转出",
};

// 工具函数：获取交易类型名称
export function getTransactionTypeName(type: number): string {
  return TRANSACTION_TYPE_NAMES[type as TransactionType] || "未知";
}

// 工具函数：获取转账类型名称
export function getTransferTypeName(type: number): string {
  return TRANSFER_TYPE_NAMES[type as TransferType] || "未知";
}

// 工具函数：获取交易类型颜色
export function getTransactionTypeColor(type: number): string {
  switch (type) {
    case TransactionType.BUY:
      return "text-red-600 dark:text-red-400";
    case TransactionType.SELL:
      return "text-green-600 dark:text-green-400";
    case TransactionType.DIVIDEND:
      return "text-blue-600 dark:text-blue-400";
    case TransactionType.SPLIT:
    case TransactionType.MERGE:
    case TransactionType.EX_RIGHTS:
      return "text-orange-600 dark:text-orange-400";
    default:
      return "text-gray-600 dark:text-gray-400";
  }
}

// 工具函数：格式化货币
export function formatCurrency(amount: number, currency = "CNY"): string {
  const symbol = currency === "CNY" ? "¥" : "$";
  return `${symbol}${amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
}

// 工具函数：格式化百分比
export function formatPercentage(value: number): string {
  const sign = value >= 0 ? "+" : "";
  return `${sign}${value.toFixed(2)}%`;
}

// 工具函数：格式化日期时间
export function formatDateTime(timestamp: number): string {
  return new Date(timestamp).toLocaleString("zh-CN");
}

// 工具函数：格式化日期
export function formatDate(timestamp: number): string {
  return new Date(timestamp).toLocaleDateString("zh-CN");
}

// 工具函数：安全地将字符串或数字转换为数字
export function toNumber(value: string | number): number {
  if (typeof value === "number") {
    return value;
  }
  const num = parseFloat(value);
  return isNaN(num) ? 0 : num;
}

// 交易表单字段类型定义
export interface TransactionFormField {
  name: string;
  label: string;
  type: "text" | "number" | "date" | "select" | "textarea";
  required: boolean;
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
  options?: { value: string | number; label: string }[];
  validation?: (value: any, formData?: any) => string;
}

// 交易表单配置
export interface TransactionFormConfig {
  type: TransactionType;
  title: string;
  submitText: string;
  fields: TransactionFormField[];
  calculateAmount?: (formData: any) => number;
}

// 通用交易表单数据
export interface UniversalTransactionData {
  type: TransactionType;
  symbol: string;
  name?: string;
  portfolioId?: number;
  time: number;
  shares?: number;
  price?: number;
  commission?: number;
  tax?: number;
  comment?: string;
  // 合股拆股特有字段
  unitShares?: number; // 多股合一或每股拆为
  // 除权除息特有字段
  unitDividend?: number; // 每10股红利
  unitIncreaseShares?: number; // 每10股转增
  unitBonusShares?: number; // 每10股送股
}
