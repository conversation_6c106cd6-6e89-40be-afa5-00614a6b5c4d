# Trash Track Web

垃圾追踪管理系统的前端Web应用，提供现代化的用户界面和交互体验。

## 技术栈

- **框架**: Next.js 15
- **UI库**: React 18
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **组件库**: Shadcn/ui
- **状态管理**: React Hooks
- **HTTP客户端**: Fetch API
- **表单处理**: React Hook Form + Zod
- **图标**: Lucide React
- **主题**: Tweakcn Tangerine

## 环境要求

- Node.js 18.0+
- npm 9.0+ 或 yarn 1.22+

## 依赖安装

### 1. 安装依赖包

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 2. 安装开发工具 (可选)

```bash
# 全局安装Next.js CLI
npm install -g next

# 安装TypeScript支持
npm install -g typescript
```

## 配置说明

### 环境变量

创建`.env.local`文件在项目根目录，包含以下配置：

```
# API接口地址
NEXT_PUBLIC_API_URL=http://localhost:8000

# 应用配置
NEXT_PUBLIC_APP_NAME=垃圾追踪管理系统
NEXT_PUBLIC_APP_VERSION=1.0.0

# 主题配置
NEXT_PUBLIC_DEFAULT_THEME=light

# 调试模式
NEXT_PUBLIC_DEBUG=false
```

### API接口配置

前端应用通过环境变量`NEXT_PUBLIC_API_URL`配置后端API地址：

- 开发环境: `http://localhost:8000`
- 生产环境: 根据实际部署地址配置

### 主题和样式配置

应用使用Tweakcn Tangerine主题，支持：

- 亮色/暗色模式切换
- 响应式设计
- 自定义颜色方案
- 组件样式定制

## 开发服务

### 启动开发服务器

```bash
# 使用npm
npm run dev

# 或使用yarn
yarn dev
```

开发服务器将在 http://localhost:3000 启动

### 开发工具说明

- **热重载**: 代码更改时自动刷新页面
- **TypeScript检查**: 实时类型检查和错误提示
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

### 端口和访问地址

- 开发服务器: http://localhost:3000
- 生产构建预览: http://localhost:3000 (使用`npm run start`)

## 构建部署

### 生产构建

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm run start
```

### 静态文件部署

```bash
# 生成静态文件 (如果配置了静态导出)
npm run export
```

### Docker部署

```bash
# 使用Docker构建
docker build -t trash-track-web .

# 运行容器
docker run -p 3000:3000 trash-track-web
```

### 性能优化建议

1. **代码分割**: Next.js自动进行代码分割
2. **图片优化**: 使用Next.js Image组件
3. **缓存策略**: 配置适当的缓存头
4. **CDN部署**: 使用CDN加速静态资源
5. **压缩**: 启用Gzip/Brotli压缩

## 功能模块

### 主要页面

#### 仪表板 (`/dashboard`)

- **概览页面** (`/dashboard/default`): 系统总览和关键指标
- **CRM仪表板** (`/dashboard/crm`): 客户关系管理
- **财务仪表板** (`/dashboard/finance`): 财务数据分析

#### 投资管理 (`/portfolio`)

- **投资组合** (`/portfolio/default`): 投资组合列表和管理
- 创建和编辑投资组合
- 交易记录管理
- 持仓分析和统计

#### 用户管理 (`/user`)

- **用户管理** (`/user/default`): 用户信息和权限管理
- 用户资料设置
- 角色和权限配置

#### 认证页面 (`/auth`)

- **登录页面** (`/auth/v1/login`, `/auth/v2/login`): 用户登录
- **注册页面** (`/auth/v1/register`, `/auth/v2/register`): 用户注册

### 主要组件

#### 布局组件

- `AppSidebar`: 侧边栏导航
- `AccountSwitcher`: 账户切换器
- `ThemeSwitcher`: 主题切换器
- `SearchDialog`: 搜索对话框

#### 业务组件

- `PortfolioPage`: 投资组合管理页面
- `UserPage`: 用户管理页面
- `LoginForm`: 登录表单
- `CreatePortfolioForm`: 创建投资组合表单

#### UI组件

- 基于Shadcn/ui的组件库
- 自定义业务组件
- 响应式布局组件

## 项目结构

```
trash-track-web/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (main)/             # 主要页面组
│   │   │   ├── dashboard/      # 仪表板页面
│   │   │   ├── portfolio/      # 投资组合页面
│   │   │   ├── user/           # 用户管理页面
│   │   │   └── auth/           # 认证页面
│   │   ├── globals.css         # 全局样式
│   │   ├── layout.tsx          # 根布局
│   │   └── page.tsx            # 首页
│   ├── components/             # 可复用组件
│   │   └── ui/                 # UI基础组件
│   ├── navigation/             # 导航配置
│   │   └── sidebar/            # 侧边栏配置
│   ├── data/                   # 静态数据
│   ├── lib/                    # 工具函数
│   ├── types/                  # TypeScript类型定义
│   └── server/                 # 服务端功能
├── public/                     # 静态资源
├── .env.local                  # 环境变量
├── next.config.mjs             # Next.js配置
├── tailwind.config.ts          # Tailwind配置
├── tsconfig.json               # TypeScript配置
└── package.json                # 项目依赖
```

## 开发指南

### 代码规范

- 使用TypeScript进行类型安全开发
- 遵循ESLint和Prettier配置
- 组件使用函数式组件和Hooks
- 使用Tailwind CSS进行样式开发

### 状态管理

- 使用React Hooks进行本地状态管理
- 认证状态通过自定义Hook管理
- API调用使用自定义Hook封装

### 路由管理

- 使用Next.js App Router
- 支持嵌套路由和布局
- 动态路由和参数传递

### API集成

- 使用Fetch API进行HTTP请求
- 统一的错误处理
- 请求和响应拦截器
- 自动token管理

## 常见问题

### 开发环境问题

- **端口冲突**: 修改package.json中的dev脚本端口
- **依赖安装失败**: 清除node_modules和package-lock.json重新安装
- **TypeScript错误**: 检查tsconfig.json配置

### API连接问题

- 检查`NEXT_PUBLIC_API_URL`环境变量
- 确认后端服务正在运行
- 验证CORS配置

### 样式问题

- 确认Tailwind CSS配置正确
- 检查组件导入路径
- 验证主题配置

### 性能问题

- 使用React DevTools分析组件渲染
- 检查不必要的重新渲染
- 优化图片和静态资源加载
